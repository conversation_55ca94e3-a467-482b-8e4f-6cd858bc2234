# Alec <PERSON>
library(optparse)
library(ggplot2)
library(forcats)
library(dplyr)


# options list for parser options
option_list <- list(
    make_option(c("-a","--figure_stats_file"), type="character", default=NULL,
            help="",
            dest="figure_stats_file"),
    make_option(c("-b","--figure_data_file"), type="character", default=NULL,
            help="",
            dest="figure_data_file"),
    make_option(c("-c","--figure_file"), type="character", default=NULL,
            help="",
            dest="figure_file")
            )

parser <- OptionParser(usage = "%prog -i data.csv etc.", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}




histogram_gmm_components = function(input_df, cancer_type){
    
# plots histogram of number of components
p = ggplot(input_df, aes(x = best_k)) + plot_theme() +
geom_histogram(binwidth = 1, color = 'black') +

ggtitle(paste0('Number of GMM Components - ', cancer_type)) +
xlab('Number of Components') + ylab('Count') +

theme(axis.text.x = element_text(angle = 0, hjust = 0.5, vjust=0.5))

print(p)

return()
}




histogram_genes_with_components = function(input_df, cancer_type){

# plot number of genes with at least n components
p = ggplot(input_df, aes(x = class, y = count)) + plot_theme() +
geom_bar(stat = 'identity') +

ggtitle(paste0('Number of genes with "n" GMM components ', cancer_type)) +
xlab('Number of components') + ylab('Number of genes') +
    
theme(axis.text.x = element_text(angle = 90))

print(p)

return()
}





pdf(opt$figure_file)

# load dfs
input_df = read.csv(opt$figure_data_file, sep='\t')
stats_df = read.csv(opt$figure_stats_file, sep='\t')

# extract cancer type from file name
parts = sub("_per_site", "", basename(opt$figure_data_file))
parts <- strsplit(parts, "_")[[1]]
cancer_type <- sub("\\.tsv$", "", parts[length(parts)])

# histogram of number of components
histogram_gmm_components(input_df, cancer_type)

# histogram the number of genes with at least n components
histogram_genes_with_components(stats_df, cancer_type)

dev.off()


print(opt$figure_file)




