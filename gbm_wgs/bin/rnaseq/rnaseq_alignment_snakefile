# /.mounts/labs/reimandlab/private/users/abahcheli/anaconda3/envs/py_dev/bin/snakemake
# Alec Ba<PERSON>cheli
from python import glob, os


## DEFINE THE FOLLOWING VARIABLES
# project directory
RNASEQ_DATA_DIR=''

# the directory with raw fastqs, each labelled
RAW_RNA_DATA_DIR=''

# barcodes for the different transcripts in fasta format
barcode_fasta=''


## THE FOLLOWING WILL BE CREATED FOR YOU
# fastq processing pass and fail directories
PASSED_FASTQ_DIR = RNASEQ_DATA_DIR + "/passed"
FAILED_FASTQ_DIR = RNASEQ_DATA_DIR + "/failed"


# reference files
RNA_SEQ_DIR='/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/ion_channels_gbm/data/rna_seq/v6'


# tools and pipelines to use
TRIMMOMATIC = 'trimmomatic'
KALLISTO = '/.mounts/labs/reimandlab/private/users/abahcheli/anaconda3/envs/wgs_nanopore/bin/kallisto'



# number of threads to use per process on average
THREADS = '20'


# list of wgs barcodes for processing
wgs_list = glob.glob(RAW_GENOMIC_FASTQ_DIR + "/*")
wgs_list = list(map(lambda x: "_".join(x.split("/")[-1].split("_")[:-1]) wgs_list))

rna_list = glob.glob(RWA_RNASEQ_FASTQ_DIR + "/*")
rna_list = list(map(lambda x: "_".join(x.split("/")[-1].split("_")[:-1]) rna_list))


###########################
# Genome Mapping Parameters
###########################
HG38_genome = "/.mounts/labs/reimandlab/private/users/abahcheli/human_genome/hg38.fa.gz"
HG38_transcriptome = "/.mounts/labs/reimandlab/private/users/abahcheli/human_genome/gencode.v38.transcripts.fa.gz"
HG_index = RNASEQ_DATA_DIR + "/hg_index"



# define the objective (make the output files)
rule all:
    input:
        # RNA seq
        expand("{results_dir}/{barcode}/abundance.tsv", barcode = rna_list, results_dir = RNA_SEQ_DIR)


###########################
# Directory Creation
###########################
rule create_directories:
    output:
        input_directory = directory(DATADIR),
        raw_data_dir = directory(RAW_DATA_DIR),
        genomic_dir = directory(GENOME_MAPPING_DIR),
        passed = directory(PASSED_FASTQ_DIR),
        failed = directory(FAILED_FASTQ_DIR)
    resources:
        threads = 1,
        runtime = '0:0:10:0',
        individual_core_memory = '5G'
    run:
        shell("mkdir -p {input.data_dir} {input.raw_data_dir} {input.genomic_dir} {input.passed} {input.failed}")
        shell("")


# create directories
rule create_directories:
    input:
        script = "/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/ion_channels_gbm/bin/2022_06_03/create_directories.py"
    output:
        DATA_DIR + "/null.txt",
        RES_DIR + "/null.txt",
        FIGURE_DATA_DIR + "/null.txt"
    resources:
        threads = 1,
        runtime = '0:0:5:0',
        individual_core_memory = '5G'
    shell:
        "{PYTHON} {input.script} --parent_directory {DIR} --version {VERSION}"



###########################
# RNA-Seq Barcode Processing
###########################

# the fastq sequences
rule fastq_barcodes:
    input:
        res_dir = RNA_SEQ_DIR + "/{barcode}/"
    output:
        outfile = RNA_SEQ_DIR + "/{barcode}/barcodes.fa"
    resources:
        threads = '1',
        runtime = '0:24:0:0',
        individual_core_memory = '5G'
    shell:
        "grep -A 1 {wildcards.barcode} {barcode_fasta} > {output.outfile}"


###########################
# Data Pre-Processing
###########################

# define fastq trimming
rule trimmomatic:
    input:
        RNASEQ_DATA_DIR,
        fastq_forward = RAW_RNA_DATA_DIR + "/{barcode}_R1.fastq.gz",
        fastq_reverse = RAW_RNA_DATA_DIR + "/{barcode}_R2.fastq.gz",
        passed_fastq_dir = PASSED_FASTQ_DIR,
        failed_fastq_dir = FAILED_FASTQ_DIR,
        barcode_fasta = RNASEQ_DATA_DIR + "/barcodes/{barcode}.fa"
    output:
        passed_1 = PASSED_FASTQ_DIR + "/{barcode}_R1.trimmed.fastq",
        passed_2 = PASSED_FASTQ_DIR + "/{barcode}_R2.trimmed.fastq",
        failed_1 = FAILED_FASTQ_DIR + "/{barcode}_R1.trimmed.fastq",
        failed_2 = FAILED_FASTQ_DIR + "/{barcode}_R2.trimmed.fastq"
    resources:
        threads = THREADS,
        runtime = '0:12:0:0',
        individual_core_memory = '3G',
    params:
        sliding_window = "4:20",
        clipping_params = ":2:40:15"
    shell:
        "{TRIMMOMATIC} PE -threads {resources.threads} {input.fastq_forward} "
        "{input.fastq_reverse} {output.passed_1} {output.failed_1} {output.passed_2} {output.failed_2} "
        "ILLUMINACLIP:{input.barcode_fasta}{params.clipping_params} SLIDINGWINDOW:{params.sliding_window}"        


###########################
# Kallisto
###########################


# define kallisto parameters
rule kallisto_counts:
    input:
        fastq_forward = PASSED_FASTQ_DIR + "/{barcode}_R1.trimmed.fastq",
        fastq_reverse = PASSED_FASTQ_DIR + "/{barcode}_R2.trimmed.fastq",
        index_file = RNA_SEQ_DIR + '/hg38.idx'
    output:
        RNA_SEQ_DIR + "/{barcode}/abundance.tsv",
        barcode_directory = directory(RNA_SEQ_DIR + "/{barcode}/")
    resources:
        threads = THREADS,
        runtime = '0:24:0:0',
        individual_core_memory = '2G'
    run:
        shell("mkdir -p {output.barcode_directory}")
        shell("{KALLISTO} quant -i {input.index_file} -o {output.barcode_directory} -b 100 --threads={resources.threads} {input.fastq_forward} {input.fastq_reverse}")







