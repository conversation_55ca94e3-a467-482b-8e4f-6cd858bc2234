# /.mounts/labs/reimandlab/private/users/abahcheli/anaconda3/envs/py_dev/bin/snakemake
# Alec Bahcheli
from python import glob, os


###########################
# Genome Mapping Parameters
###########################
HG38_genome = "/.mounts/labs/reimandlab/private/users/abahcheli/human_genome/hg38.fa.gz"
HG38_transcriptome = "/.mounts/labs/reimandlab/private/users/abahcheli/human_genome/gencode.v38.transcripts.fa.gz"

# tools and pipelines to use
TRIMMOMATIC = 'trimmomatic'
KALLISTO = '/.mounts/labs/reimandlab/private/users/abahcheli/anaconda3/envs/wgs_nanopore/bin/kallisto'

# number of threads to use per process on average
THREADS = '20'



###########################
# MAKE THESE BEFORE RUNNING
###########################

# project directory
RNA_SEQ_DIR=''

# barcodes for the different transcripts: in fasta format by file name / barcode
barcode_fasta=''

# eg for barcode_fasta
# >729_Scr_a
# ATCACG
# >729_Scr_b
# CGATGT
# >729_Scr_c
# TTAGGC
# >729_A3_a
# TGACCA
# >729_A3_b
# ACAGTG


###########################
# Data Pre-Processing
###########################
# define fastq trimming
rule trimmomatic:
    input:
        # make sure the directory exists
        RNA_SEQ_DIR,
        fastq_forward = RAW_RNA_DATA_DIR + "/{barcode}_R1.fastq.gz",
        fastq_reverse = RAW_RNA_DATA_DIR + "/{barcode}_R2.fastq.gz",
        # this is the sequencing barcode for the specific sample: you can often find it based on the details of the seuqencing run
        barcode_fasta = RNA_SEQ_DIR + "/{barcode}.fa"
    output:
        passed_fastq_dir = RNA_SEQ_DIR + "/passed",
        failed_fastq_dir = RNA_SEQ_DIR + "/failed",
        passed_1 = RNA_SEQ_DIR + "/passed" + "/{barcode}_R1.trimmed.fastq",
        passed_2 = RNA_SEQ_DIR + "/passed" + "/{barcode}_R2.trimmed.fastq",
        failed_1 = RNA_SEQ_DIR + "/failed" + "/{barcode}_R1.trimmed.fastq",
        failed_2 = RNA_SEQ_DIR + "/failed" + "/{barcode}_R2.trimmed.fastq"
    resources:
        threads = THREADS,
        runtime = '0:12:0:0',
        individual_core_memory = '3G',
    params:
        sliding_window = "4:20",
        clipping_params = ":2:40:15"
    run:
        shell("mkdir -p {output.passed_fastq_dir} {output.failed_fastq_dir}")
        shell("{TRIMMOMATIC} PE -threads {resources.threads} {input.fastq_forward} {input.fastq_reverse} {output.passed_1} {output.failed_1} {output.passed_2} {output.failed_2} ILLUMINACLIP:{input.barcode_fasta}{params.clipping_params} SLIDINGWINDOW:{params.sliding_window}")



###########################
# Kallisto
###########################

# create an index file
rule kallisto_index:
    input:
        HG38_transcriptome,
        RNA_SEQ_DIR
    output:
        index_file = RNA_SEQ_DIR + '/hg38.idx'
    resources:
        threads = 1,
        runtime = '0:24:0:0',
        individual_core_memory = '30G'
    run:
        shell("cd {RNA_SEQ_DIR}")
        shell("{KALLISTO} index -i {input.HG38_transcriptome}")


# define kallisto parameters
rule kallisto_counts:
    input:
        fastq_forward = PASSED_FASTQ_DIR + "/{barcode}_R1.trimmed.fastq",
        fastq_reverse = PASSED_FASTQ_DIR + "/{barcode}_R2.trimmed.fastq",
        index_file = RNA_SEQ_DIR + '/hg38.idx'
    output:
        RNA_SEQ_DIR + "/{barcode}/abundance.tsv",
        barcode_directory = directory(RNA_SEQ_DIR + "/{barcode}/")
    resources:
        threads = THREADS,
        runtime = '0:24:0:0',
        individual_core_memory = '2G'
    run:
        shell("mkdir -p {output.barcode_directory}")
        shell("{KALLISTO} quant -i {input.index_file} -o {output.barcode_directory} -b 100 --threads={resources.threads} {input.fastq_forward} {input.fastq_reverse}")







