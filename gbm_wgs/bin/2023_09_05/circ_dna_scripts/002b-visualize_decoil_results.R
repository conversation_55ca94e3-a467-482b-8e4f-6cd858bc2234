# <PERSON>
library(optparse)
library(ggplot2)
library(dplyr)
library(tidyr)
library(readr)
library(scales)
library(RColorBrewer)
library(viridis)
library(patchwork)

# options list for parser options
option_list <- list(
    make_option(c("-a","--input_file"), type="character", default=NULL,
            help="Combined decoil results TSV file",
            dest="input_file"),
    make_option(c("-b","--output_prefix"), type="character", default=NULL,
            help="Prefix for output files (without extension)",
            dest="output_prefix")
)

parser <- OptionParser(usage = "%prog -a input.tsv -b output_prefix", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}

create_decoil_plots = function(data) {
    # Prepare data for plotting
    data <- data %>%
        mutate(
            patient = gsub("-.*", "", sample_name),
            tumor_type = gsub(".*-", "", sample_name),
            log10_size_mb = log10(size_mb + 0.001),
            log10_coverage = log10(mean_coverage + 1),
            size_category = case_when(
                size_mb < 0.01 ~ "Small (<10kb)",
                size_mb < 0.1 ~ "Medium (10kb-100kb)", 
                size_mb < 1 ~ "Large (100kb-1MB)",
                TRUE ~ "Very Large (>1MB)"
            )
        )
    
    # Define colors
    tumor_colors <- c("primary" = "#E31A1C", "recurrent" = "#1F78B4")
    
    # 1. ecDNA count distribution
    ecdna_counts <- data %>%
        filter(is_ecdna == TRUE) %>%
        count(sample_name, name = "ecdna_count") %>%
        mutate(
            patient = gsub("-.*", "", sample_name),
            tumor_type = gsub(".*-", "", sample_name)
        )
    
    p1 <- ggplot(ecdna_counts, aes(x = ecdna_count, fill = tumor_type)) +
        geom_histogram(binwidth = 1, alpha = 0.7, position = "identity") +
        scale_fill_manual(values = tumor_colors, name = "Tumor Type") +
        labs(title = "Distribution of ecDNA Counts per Sample",
             x = "Number of ecDNAs per Sample", y = "Number of Samples") +
        plot_theme()
    
    # 2. Size distribution
    ecdna_data <- data %>% filter(is_ecdna == TRUE)
    
    if (nrow(ecdna_data) > 0) {
        p2 <- ggplot(ecdna_data, aes(x = size_mb, fill = sample_name)) +
            geom_histogram(bins = 20, alpha = 0.7) +
            scale_x_continuous(labels = scales::comma) +
            labs(title = "ecDNA Size Distribution",
                 x = "Size (MB)", y = "Count") +
            plot_theme()
    } else {
        p2 <- ggplot() +
            geom_text(aes(x = 0.5, y = 0.5, label = "No ecDNAs detected"), size = 4) +
            xlim(0, 1) + ylim(0, 1) + theme_void() +
            labs(title = "ecDNA Size Distribution")
    }
    
    # 3. Size vs Coverage scatter
    if (nrow(ecdna_data) > 0) {
        p3 <- ggplot(ecdna_data, aes(x = size_mb, y = mean_coverage, 
                                     color = sample_name, shape = tumor_type)) +
            geom_point(size = 4, alpha = 0.8) +
            scale_shape_manual(values = c("primary" = 16, "recurrent" = 17),
                               name = "Tumor Type") +
            scale_x_continuous(labels = scales::comma) +
            scale_y_continuous(labels = scales::comma) +
            labs(title = "ecDNA Size vs Mean Coverage",
                 x = "Size (MB)", y = "Mean Coverage") +
            plot_theme()
    } else {
        p3 <- ggplot() +
            geom_text(aes(x = 0.5, y = 0.5, label = "No ecDNAs detected"), size = 4) +
            xlim(0, 1) + ylim(0, 1) + theme_void() +
            labs(title = "ecDNA Size vs Coverage")
    }
    
    # Combine plots
    combined_plot <- p1 / p2 / p3
    combined_plot <- combined_plot +
        plot_annotation(
            title = "Decoil ecDNA Analysis Summary",
            subtitle = paste("Analysis of", sum(data$is_ecdna, na.rm = TRUE), "ecDNAs from",
                             nrow(data), "total circular DNAs across",
                             length(unique(data$sample_name)), "samples")
        )
    
    return(combined_plot)
}

# Load data
data <- read_csv(opt$input_file)

# Create plots
pdf(paste0(opt$output_prefix, "_summary.pdf"), width = 15, height = 20)
combined_plot <- create_decoil_plots(data)
print(combined_plot)
dev.off()

# Create summary stats
summary_stats <- data %>%
    group_by(sample_name) %>%
    summarise(
        total_circular_dnas = n(),
        ecdnas = sum(is_ecdna, na.rm = TRUE),
        mean_size_mb = mean(size_mb, na.rm = TRUE),
        median_size_mb = median(size_mb, na.rm = TRUE),
        mean_coverage = mean(mean_coverage, na.rm = TRUE),
        .groups = 'drop'
    )

write_tsv(summary_stats, paste0(opt$output_prefix, "_summary_stats.tsv"))

print(paste0(opt$output_prefix, "_summary.pdf"))


