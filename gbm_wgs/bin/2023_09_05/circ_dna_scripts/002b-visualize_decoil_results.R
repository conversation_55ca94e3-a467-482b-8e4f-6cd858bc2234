# <PERSON>
library(optparse)
library(ggplot2)

library(dplyr)
library(RColorBrewer)
library(viridis)

# options list for parser options
option_list <- list(
    make_option(c("-a","--ecdna_counts"), type="character", default=NULL,
            help="Combined decoil results TSV file",
            dest="ecdna_counts"),
    make_option(c("-b","--summary_results"), type="character", default=NULL,
            help="Combined decoil results TSV file",
            dest="summary_results"),
    make_option(c("-c","--summary_plot"), type="character", default=NULL,
            help="Summary plot PDF file",
            dest="summary_plot")
)

parser <- OptionParser(usage = "%prog -a input.tsv -b summary_plot", option_list=option_list)

# get the parse information and assign to groups in the opt variable
opt = parse_args(parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}

create_decoil_plots = function(input_df, counts_df) {

# Define colors
tumor_colors <- c("primary" = "#E31A1C", "recurrent" = "#1F78B4")

# 1. ecDNA count distribution
    
p <- ggplot(counts_df, aes(x = sample_name, y = n_ecdnas)) +
geom_bar(stat = "identity", color='black') +
geom_text(aes(label = n_ecdnas), vjust = -0.5) +

scale_fill_manual(values = tumor_colors, name = "Tumor Type") +

labs(title = "ecDNA counts per sample", y = "Number of ecDNAs") +

plot_theme() +
theme(axis.text.x = element_text(angle = 90, hjust = 1, vjust = 0.5))

    
print(p)

    
# 2. Size distribution

# remove NAs
ecdna_data = input_df[input_df$chr_origin != 0,]

p <- ggplot(ecdna_data, aes(x = size_bp/1000000, fill = sample_name)) +
geom_histogram(bins = 20, alpha = 0.7) +

labs(title = "ecDNA Size Distribution", x = "Size (Mb)", y = "Count") +

plot_theme() +
theme(axis.text.x = element_text(angle = 90, hjust = 1, vjust = 0.5))

    
print(p)

    
# # 3. Size vs Coverage scatter
# p <- ggplot(ecdna_data, aes(x = size_bp, y = copy_number, color = sample_name, shape = tumor_type)) +
# geom_point(size = 4, alpha = 0.8) +

# scale_shape_manual(values = c("primary" = 16, "recurrent" = 17), name = "Tumor Type") +
# scale_x_continuous(labels = scales::comma) +
# scale_y_continuous(labels = scales::comma) +

# labs(title = "ecDNA Size vs Mean Coverage", x = "Size (MB)", y = "Mean Coverage") +

# plot_theme()

    
# print(p)
    
return()
}



pdf(opt$summary_plot)

# Load data
counts_df <- read.csv(opt$ecdna_counts, sep='\t')
summary_df = read.csv(opt$summary_results, sep='\t')


# Factor
# Function to extract numeric parts from each element
extract_numeric <- function(x) as.numeric(gsub("\\D", "", x))

# Extract and sort by numeric parts
sorted_vector <- unique(summary_df$patient)[order(sapply(unique(summary_df$patient), extract_numeric))]
summary_df$patient = factor(summary_df$patient, levels = sorted_vector)

# factor counts_df 
sorted_vector <- unique(counts_df$sample_name)[order(sapply(unique(counts_df$sample_name), extract_numeric))]
counts_df$sample_name = factor(counts_df$sample_name, levels = sorted_vector)


# Create plots
create_decoil_plots(summary_df, counts_df)

dev.off()



