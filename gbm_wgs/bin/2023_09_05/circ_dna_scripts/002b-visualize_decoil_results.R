#!/usr/bin/env Rscript
# Script to visualize Decoil results
# Author: <PERSON>
# Date: 2023-09-05

# Load required libraries
suppressPackageStartupMessages({
  library(ggplot2)
  library(dplyr)
  library(tidyr)
  library(readr)
  library(scales)
  library(RColorBrewer)
  library(viridis)
  library(patchwork)
})

# Parse command line arguments
args <- commandArgs(trailingOnly = TRUE)

if (length(args) < 2) {
  cat("Usage: Rscript 002b-visualize_decoil_results.R <input_file> <output_prefix>\n")
  cat("  input_file: Combined decoil results TSV file\n")
  cat("  output_prefix: Prefix for output files (without extension)\n")
  quit(status = 1)
}

input_file <- args[1]
output_prefix <- args[2]

cat("=== Decoil Results Visualizer ===\n")
cat("Input file:", input_file, "\n")
cat("Output prefix:", output_prefix, "\n")

# Check if input file exists
if (!file.exists(input_file)) {
  cat("Error: Input file does not exist:", input_file, "\n")
  quit(status = 1)
}

# Load data
cat("Loading data...\n")
data <- read_tsv(input_file, show_col_types = FALSE)

cat("Data dimensions:", nrow(data), "rows,", ncol(data), "columns\n")

# Check if data is empty
if (nrow(data) == 0) {
  cat("Warning: No data found in input file. Creating empty plots.\n")
  
  # Create empty plot
  empty_plot <- ggplot() + 
    geom_text(aes(x = 0.5, y = 0.5, label = "No circular DNAs detected"), 
              size = 6, hjust = 0.5, vjust = 0.5) +
    xlim(0, 1) + ylim(0, 1) +
    theme_void() +
    labs(title = "Decoil Results Summary",
         subtitle = "No circular DNAs detected in any samples")
  
  ggsave(paste0(output_prefix, "_summary.pdf"), empty_plot, 
         width = 8, height = 6, units = "in")
  
  cat("Empty summary plot saved to:", paste0(output_prefix, "_summary.pdf"), "\n")
  quit(status = 0)
}

# Print data summary
cat("\nData Summary:\n")
cat("Samples:", length(unique(data$sample_name)), "\n")
cat("Total circular DNAs:", nrow(data), "\n")
cat("Potential ecDNAs:", sum(data$is_ecdna, na.rm = TRUE), "\n")
cat("Size range:", min(data$size_mb, na.rm = TRUE), "-", 
    max(data$size_mb, na.rm = TRUE), "MB\n")
cat("Coverage range:", min(data$mean_coverage, na.rm = TRUE), "-", 
    max(data$mean_coverage, na.rm = TRUE), "\n")

# Prepare data for plotting
data <- data %>%
  mutate(
    patient = gsub("-.*", "", sample_name),
    tumor_type = gsub(".*-", "", sample_name),
    log10_size_mb = log10(size_mb + 0.001),  # Add small value to avoid log(0)
    log10_coverage = log10(mean_coverage + 1),
    size_category = case_when(
      size_mb < 0.01 ~ "Small (<10kb)",
      size_mb < 0.1 ~ "Medium (10kb-100kb)", 
      size_mb < 1 ~ "Large (100kb-1MB)",
      TRUE ~ "Very Large (>1MB)"
    ),
    coverage_category = case_when(
      mean_coverage < 10 ~ "Low (<10x)",
      mean_coverage < 50 ~ "Medium (10-50x)",
      mean_coverage < 100 ~ "High (50-100x)",
      TRUE ~ "Very High (>100x)"
    )
  )

# Color palettes
patient_colors <- rainbow(length(unique(data$patient)))
names(patient_colors) <- unique(data$patient)

tumor_colors <- c("primary" = "#E31A1C", "recurrent" = "#1F78B4")

# 1. Circular DNA counts per sample
cat("Creating circular DNA count plot...\n")
count_data <- data %>%
  group_by(sample_name, patient, tumor_type) %>%
  summarise(
    total_circdna = n(),
    ecdna_count = sum(is_ecdna, na.rm = TRUE),
    .groups = "drop"
  ) %>%
  pivot_longer(cols = c(total_circdna, ecdna_count), 
               names_to = "type", values_to = "count") %>%
  mutate(type = case_when(
    type == "total_circdna" ~ "All Circular DNA",
    type == "ecdna_count" ~ "Potential ecDNA"
  ))

p1 <- ggplot(count_data, aes(x = reorder(sample_name, count), 
                             y = count, fill = interaction(tumor_type, type))) +
  geom_col(position = "dodge", alpha = 0.8) +
  scale_fill_manual(values = c("#E31A1C", "#FF7F7F", "#1F78B4", "#7FC7FF"), 
                    name = "Type") +
  coord_flip() +
  labs(title = "Number of Circular DNAs per Sample",
       x = "Sample", y = "Count") +
  theme_minimal() +
  theme(axis.text.y = element_text(size = 8))

# 2. Size distribution
cat("Creating size distribution plot...\n")
p2 <- ggplot(data, aes(x = size_mb, fill = tumor_type)) +
  geom_histogram(bins = 30, alpha = 0.7, position = "identity") +
  scale_fill_manual(values = tumor_colors, name = "Tumor Type") +
  scale_x_continuous(trans = "log10", labels = scales::comma) +
  labs(title = "Distribution of Circular DNA Sizes",
       x = "Size (MB, log10 scale)", y = "Count") +
  theme_minimal()

# 3. Coverage distribution
cat("Creating coverage distribution plot...\n")
p3 <- ggplot(data, aes(x = mean_coverage, fill = tumor_type)) +
  geom_histogram(bins = 30, alpha = 0.7, position = "identity") +
  scale_fill_manual(values = tumor_colors, name = "Tumor Type") +
  scale_x_continuous(trans = "log10", labels = scales::comma) +
  labs(title = "Distribution of Mean Coverage",
       x = "Mean Coverage (log10 scale)", y = "Count") +
  theme_minimal()

# 4. Size vs Coverage scatter plot
cat("Creating size vs coverage scatter plot...\n")
p4 <- ggplot(data, aes(x = size_mb, y = mean_coverage, 
                       color = patient, shape = tumor_type, size = is_ecdna)) +
  geom_point(alpha = 0.8) +
  scale_color_manual(values = patient_colors, name = "Patient") +
  scale_shape_manual(values = c("primary" = 16, "recurrent" = 17), 
                     name = "Tumor Type") +
  scale_size_manual(values = c("TRUE" = 3, "FALSE" = 1.5), 
                    name = "Potential ecDNA", labels = c("No", "Yes")) +
  scale_x_continuous(trans = "log10", labels = scales::comma) +
  scale_y_continuous(trans = "log10", labels = scales::comma) +
  labs(title = "Circular DNA Size vs Coverage",
       x = "Size (MB, log10 scale)", y = "Mean Coverage (log10 scale)") +
  theme_minimal()

# 5. ecDNA analysis
cat("Creating ecDNA analysis plot...\n")
ecdna_data <- data %>%
  group_by(sample_name, tumor_type) %>%
  summarise(
    total_circdna = n(),
    ecdna_count = sum(is_ecdna, na.rm = TRUE),
    prop_ecdna = ecdna_count / total_circdna,
    .groups = "drop"
  )

p5 <- ggplot(ecdna_data, aes(x = reorder(sample_name, prop_ecdna), 
                             y = prop_ecdna, fill = tumor_type)) +
  geom_col(alpha = 0.8) +
  scale_fill_manual(values = tumor_colors, name = "Tumor Type") +
  scale_y_continuous(labels = scales::percent) +
  coord_flip() +
  labs(title = "Proportion of Potential ecDNAs",
       x = "Sample", y = "Proportion of ecDNAs") +
  theme_minimal() +
  theme(axis.text.y = element_text(size = 8))

# 6. Size and coverage categories
cat("Creating category analysis plot...\n")
category_data <- data %>%
  count(size_category, coverage_category, tumor_type) %>%
  mutate(
    size_category = factor(size_category, levels = c("Small (<10kb)", "Medium (10kb-100kb)", 
                                                    "Large (100kb-1MB)", "Very Large (>1MB)")),
    coverage_category = factor(coverage_category, levels = c("Low (<10x)", "Medium (10-50x)", 
                                                           "High (50-100x)", "Very High (>100x)"))
  )

p6 <- ggplot(category_data, aes(x = size_category, y = coverage_category, 
                                fill = n, size = n)) +
  geom_point(shape = 21, alpha = 0.8) +
  scale_fill_viridis_c(name = "Count") +
  scale_size_continuous(name = "Count", range = c(1, 10)) +
  facet_wrap(~tumor_type) +
  labs(title = "Circular DNA Size vs Coverage Categories",
       x = "Size Category", y = "Coverage Category") +
  theme_minimal() +
  theme(axis.text.x = element_text(angle = 45, hjust = 1))

# 7. Summary statistics table
cat("Creating summary statistics...\n")
summary_stats <- data %>%
  group_by(tumor_type) %>%
  summarise(
    n_samples = n_distinct(sample_name),
    n_circdna = n(),
    n_ecdna = sum(is_ecdna, na.rm = TRUE),
    mean_size_mb = mean(size_mb, na.rm = TRUE),
    median_size_mb = median(size_mb, na.rm = TRUE),
    mean_coverage = mean(mean_coverage, na.rm = TRUE),
    median_coverage = median(mean_coverage, na.rm = TRUE),
    .groups = "drop"
  ) %>%
  mutate(across(where(is.numeric), ~round(.x, 3)))

# Convert to long format for plotting
summary_long <- summary_stats %>%
  select(-n_samples) %>%
  pivot_longer(cols = -tumor_type, names_to = "metric", values_to = "value") %>%
  mutate(
    metric_label = case_when(
      metric == "n_circdna" ~ "Total Circular DNAs",
      metric == "n_ecdna" ~ "Potential ecDNAs",
      metric == "mean_size_mb" ~ "Mean Size (MB)",
      metric == "median_size_mb" ~ "Median Size (MB)",
      metric == "mean_coverage" ~ "Mean Coverage",
      metric == "median_coverage" ~ "Median Coverage",
      TRUE ~ metric
    )
  )

p7 <- ggplot(summary_long, aes(x = metric_label, y = value, fill = tumor_type)) +
  geom_col(position = "dodge", alpha = 0.8) +
  scale_fill_manual(values = tumor_colors, name = "Tumor Type") +
  coord_flip() +
  labs(title = "Summary Statistics by Tumor Type",
       x = "Metric", y = "Value") +
  theme_minimal() +
  theme(axis.text.y = element_text(size = 10))

# Combine plots
cat("Combining plots...\n")
combined_plot <- (p1 + p2) / (p3 + p4) / (p5 + p6) / p7
combined_plot <- combined_plot + 
  plot_annotation(
    title = "Decoil Results Summary",
    subtitle = paste("Analysis of", nrow(data), "circular DNAs from", 
                     length(unique(data$sample_name)), "samples"),
    theme = theme(plot.title = element_text(size = 16, hjust = 0.5),
                  plot.subtitle = element_text(size = 12, hjust = 0.5))
  )

# Save plots
cat("Saving plots...\n")

# Combined plot
ggsave(paste0(output_prefix, "_summary.pdf"), combined_plot, 
       width = 16, height = 24, units = "in")

# Individual plots
ggsave(paste0(output_prefix, "_counts.pdf"), p1, 
       width = 10, height = 8, units = "in")
ggsave(paste0(output_prefix, "_sizes.pdf"), p2, 
       width = 8, height = 6, units = "in")
ggsave(paste0(output_prefix, "_coverage.pdf"), p3, 
       width = 8, height = 6, units = "in")
ggsave(paste0(output_prefix, "_scatter.pdf"), p4, 
       width = 10, height = 8, units = "in")
ggsave(paste0(output_prefix, "_ecdna_prop.pdf"), p5, 
       width = 10, height = 8, units = "in")
ggsave(paste0(output_prefix, "_categories.pdf"), p6, 
       width = 12, height = 8, units = "in")
ggsave(paste0(output_prefix, "_statistics.pdf"), p7, 
       width = 10, height = 6, units = "in")

# Save summary table
write_tsv(summary_stats, paste0(output_prefix, "_summary_stats.tsv"))

cat("\n=== VISUALIZATION COMPLETE ===\n")
cat("Files created:\n")
cat("  Combined plot:", paste0(output_prefix, "_summary.pdf"), "\n")
cat("  Individual plots:", paste0(output_prefix, "_*.pdf"), "\n")
cat("  Summary statistics:", paste0(output_prefix, "_summary_stats.tsv"), "\n")

cat("\nDone!\n")
