# <PERSON> Bahcheli
import pandas as pd
import os
import sys
import argparse
import subprocess

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Intersect ecDNA regions with protein-coding genes"
    )
    parser.add_argument(
        "--ecdna_bed_file",
        required=True,
        help="Path to ecDNA BED file (reconstruct.ecDNA.filtered.bed)"
    )
    parser.add_argument(
        "--genes_bed_file", 
        required=True,
        help="Path to protein-coding genes BED file"
    )
    parser.add_argument(
        "--sample_name",
        required=True,
        help="Sample name"
    )
    parser.add_argument(
        "--output_file",
        required=True,
        help="Output BED file with intersected genes"
    )
    parser.add_argument(
        "--fasta_file",
        help="Optional: ecDNA FASTA file for sequence information"
    )
    
    return parser.parse_args()

def load_ecdna_bed(bed_file):
    """Load ecDNA BED file"""
    if not os.path.exists(bed_file):
        print(f"Warning: ecDNA BED file not found: {bed_file}")
        return pd.DataFrame()
    
    try:
        # Read the file and handle the header line starting with #
        with open(bed_file, 'r') as f:
            lines = f.readlines()
        
        # Find the header line and data lines
        header_line = None
        data_lines = []
        
        for line in lines:
            line = line.strip()
            if line.startswith('#'):
                # This is the header line, extract column names
                header_line = line[1:].split('\t')  # Remove # and split
            elif line:  # Non-empty data line
                data_lines.append(line.split('\t'))
        
        if not data_lines:
            print(f"No data found in ecDNA BED file: {bed_file}")
            return pd.DataFrame()
        
        # Create DataFrame
        if header_line:
            df = pd.DataFrame(data_lines, columns=header_line)
        else:
            # Use default column names if no header found
            expected_columns = ['chr', 'start', 'end', 'circ_id', 'fragment_id', 'strand', 'coverage', 'estimated_proportions']
            df = pd.DataFrame(data_lines, columns=expected_columns[:len(data_lines[0])])
        
        # Convert numeric columns
        numeric_columns = ['start', 'end', 'circ_id', 'fragment_id', 'coverage', 'estimated_proportions']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')
        
        print(f"Loaded ecDNA BED file: {bed_file} ({len(df)} regions)")
        return df
    except Exception as e:
        print(f"Error loading ecDNA BED file {bed_file}: {e}")
        return pd.DataFrame()

def load_genes_bed(bed_file):
    """Load protein-coding genes BED file"""
    if not os.path.exists(bed_file):
        print(f"Error: Genes BED file not found: {bed_file}")
        return pd.DataFrame()
    
    try:
        df = pd.read_csv(bed_file, sep='\t')
        print(f"Loaded genes BED file: {bed_file} ({len(df)} genes)")
        return df
    except Exception as e:
        print(f"Error loading genes BED file {bed_file}: {e}")
        return pd.DataFrame()

def intersect_regions(ecdna_df, genes_df):
    """Find genes that intersect with ecDNA regions"""
    intersections = []
    
    for _, ecdna_row in ecdna_df.iterrows():
        ecdna_chr = ecdna_row['chr']
        ecdna_start = int(ecdna_row['start'])
        ecdna_end = int(ecdna_row['end'])
        circ_id = ecdna_row['circ_id']
        coverage = ecdna_row.get('coverage', 0)
        
        # Find genes on the same chromosome that overlap with this ecDNA region
        matching_genes = genes_df[
            (genes_df['chr'].astype(str) == str(ecdna_chr)) &
            (genes_df['start'] < ecdna_end) &
            (genes_df['end'] > ecdna_start)
        ]
        
        for _, gene_row in matching_genes.iterrows():
            # Calculate overlap
            overlap_start = max(ecdna_start, int(gene_row['start']))
            overlap_end = min(ecdna_end, int(gene_row['end']))
            overlap_length = overlap_end - overlap_start
            
            if overlap_length > 0:
                gene_length = int(gene_row['end']) - int(gene_row['start'])
                overlap_fraction = overlap_length / gene_length
                
                intersections.append({
                    'chr': ecdna_chr,
                    'start': overlap_start,
                    'end': overlap_end,
                    'gene': gene_row['gene'],
                    'circ_id': circ_id,
                    'coverage': coverage,
                    'gene_start': int(gene_row['start']),
                    'gene_end': int(gene_row['end']),
                    'gene_length': gene_length,
                    'overlap_length': overlap_length,
                    'overlap_fraction': round(overlap_fraction, 3),
                    'ecdna_start': ecdna_start,
                    'ecdna_end': ecdna_end,
                    'ecdna_length': ecdna_end - ecdna_start
                })
    
    if intersections:
        return pd.DataFrame(intersections)
    else:
        return pd.DataFrame()

def load_fasta_info(fasta_file):
    """Load FASTA file information if available"""
    if not fasta_file or not os.path.exists(fasta_file):
        return {}
    
    fasta_info = {}
    try:
        with open(fasta_file, 'r') as f:
            current_id = None
            current_seq = []
            
            for line in f:
                line = line.strip()
                if line.startswith('>'):
                    # Save previous sequence
                    if current_id:
                        fasta_info[current_id] = {
                            'sequence_length': len(''.join(current_seq)),
                            'header': current_id
                        }
                    
                    # Start new sequence
                    current_id = line[1:]  # Remove '>'
                    current_seq = []
                else:
                    current_seq.append(line)
            
            # Save last sequence
            if current_id:
                fasta_info[current_id] = {
                    'sequence_length': len(''.join(current_seq)),
                    'header': current_id
                }
        
        print(f"Loaded FASTA info: {fasta_file} ({len(fasta_info)} sequences)")
    except Exception as e:
        print(f"Warning: Could not load FASTA file {fasta_file}: {e}")
    
    return fasta_info

def main():
    """Main function"""
    args = parse_arguments()
    
    print("=== ecDNA Gene Intersection Analysis ===")
    print(f"Sample: {args.sample_name}")
    print(f"ecDNA BED file: {args.ecdna_bed_file}")
    print(f"Genes BED file: {args.genes_bed_file}")
    print(f"Output file: {args.output_file}")
    
    # Load input files
    ecdna_df = load_ecdna_bed(args.ecdna_bed_file)
    genes_df = load_genes_bed(args.genes_bed_file)
    
    if ecdna_df.empty:
        print("No ecDNA regions found. Creating empty output file.")
        empty_df = pd.DataFrame(columns=[
            'chr', 'start', 'end', 'gene', 'circ_id', 'coverage',
            'gene_start', 'gene_end', 'gene_length', 'overlap_length', 
            'overlap_fraction', 'ecdna_start', 'ecdna_end', 'ecdna_length'
        ])
        empty_df['sample_name'] = args.sample_name
        empty_df.to_csv(args.output_file, sep='\t', index=False)
        return
    
    if genes_df.empty:
        print("No genes found. Cannot perform intersection.")
        return
    
    # Perform intersection
    intersections_df = intersect_regions(ecdna_df, genes_df)
    
    if intersections_df.empty:
        print("No gene-ecDNA intersections found.")
        empty_df = pd.DataFrame(columns=[
            'chr', 'start', 'end', 'gene', 'circ_id', 'coverage',
            'gene_start', 'gene_end', 'gene_length', 'overlap_length', 
            'overlap_fraction', 'ecdna_start', 'ecdna_end', 'ecdna_length'
        ])
        empty_df['sample_name'] = args.sample_name
        empty_df.to_csv(args.output_file, sep='\t', index=False)
        return
    
    # Add sample name
    intersections_df['sample_name'] = args.sample_name
    
    # Load FASTA info if available
    if args.fasta_file:
        fasta_info = load_fasta_info(args.fasta_file)
        # Could add FASTA sequence information here if needed
    
    # Sort by chromosome, start position
    intersections_df = intersections_df.sort_values(['chr', 'start'])
    
    # Create output directory if needed
    os.makedirs(os.path.dirname(args.output_file), exist_ok=True)
    
    # Save results
    intersections_df.to_csv(args.output_file, sep='\t', index=False)
    
    print(f"\n=== RESULTS ===")
    print(f"Total ecDNA regions: {len(ecdna_df)}")
    print(f"Total genes intersected: {len(intersections_df)}")
    print(f"Unique genes: {intersections_df['gene'].nunique()}")
    print(f"Results saved to: {args.output_file}")
    
    # Print summary by circ_id
    if len(intersections_df) > 0:
        print(f"\nGenes per ecDNA:")
        genes_per_ecdna = intersections_df.groupby('circ_id')['gene'].nunique().sort_values(ascending=False)
        for circ_id, gene_count in genes_per_ecdna.head(10).items():
            print(f"  ecDNA {circ_id}: {gene_count} genes")

if __name__ == "__main__":
    main()
