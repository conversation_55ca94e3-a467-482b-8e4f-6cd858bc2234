#!/usr/bin/env python3
"""
Script to combine Decoil results from multiple samples
Author: <PERSON>
Date: 2023-09-05

This script combines the reconstruct.ecDNA.filtered.bed and summary.txt files
from Decoil results into a single summary file.

Input files:
- reconstruct.ecDNA.filtered.bed: Contains ecDNA regions with coverage
- summary.txt: Contains ecDNA summary information with sizes and topology

Output:
- Combined TSV file with circ_id, amplicon size, sample name, max region size, and coverage
"""

import pandas as pd
import argparse
import os
import sys
from pathlib import Path
import numpy as np


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Combine Decoil results from multiple samples"
    )
    parser.add_argument(
        "--results_dir",
        required=True,
        help="Base results directory containing analysis_nanopore/ecdna/decoil subdirectories"
    )
    parser.add_argument(
        "--sample_list",
        required=True,
        help="Comma-separated list of samples (e.g., RLGS1-primary,RLGS2-primary)"
    )
    parser.add_argument(
        "--output_file",
        required=True,
        help="Output TSV file path"
    )
    parser.add_argument(
        "--test_mode",
        action="store_true",
        help="Test mode using example directory"
    )
    parser.add_argument(
        "--test_directory",
        help="Path to test directory containing decoil results"
    )
    
    return parser.parse_args()


def load_summary_file(file_path):
    """Load and process summary.txt file"""
    if not os.path.exists(file_path):
        print(f"Warning: Summary file not found: {file_path}")
        return pd.DataFrame()
    
    try:
        df = pd.read_csv(file_path, sep='\t')
        print(f"Loaded summary: {file_path} ({len(df)} rows)")
        return df
    except Exception as e:
        print(f"Error loading summary file {file_path}: {e}")
        return pd.DataFrame()


def load_bed_file(file_path):
    """Load and process reconstruct.ecDNA.filtered.bed file"""
    if not os.path.exists(file_path):
        print(f"Warning: BED file not found: {file_path}")
        return pd.DataFrame()

    try:
        # Read the file and handle the header line starting with #
        with open(file_path, 'r') as f:
            lines = f.readlines()

        # Find the header line and data lines
        header_line = None
        data_lines = []

        for line in lines:
            line = line.strip()
            if line.startswith('#'):
                # This is the header line, extract column names
                header_line = line[1:].split('\t')  # Remove # and split
            elif line:  # Non-empty data line
                data_lines.append(line.split('\t'))

        if not data_lines:
            print(f"No data found in BED file: {file_path}")
            return pd.DataFrame()

        # Create DataFrame
        if header_line:
            df = pd.DataFrame(data_lines, columns=header_line)
        else:
            # Use default column names if no header found
            expected_columns = ['chr', 'start', 'end', 'circ_id', 'fragment_id', 'strand', 'coverage', 'estimated_proportions']
            df = pd.DataFrame(data_lines, columns=expected_columns[:len(data_lines[0])])

        # Convert numeric columns
        numeric_columns = ['start', 'end', 'circ_id', 'fragment_id', 'coverage', 'estimated_proportions']
        for col in numeric_columns:
            if col in df.columns:
                df[col] = pd.to_numeric(df[col], errors='coerce')

        print(f"Loaded BED file: {file_path} ({len(df)} rows)")
        return df
    except Exception as e:
        print(f"Error loading BED file {file_path}: {e}")
        return pd.DataFrame()


def process_sample(sample_name, results_dir, test_mode=False, test_directory=None):
    """Process a single sample and return combined data"""
    
    if test_mode:
        sample_dir = test_directory
        sample_name = "RLGS2-primary"  # Use the test sample name
    else:
        # Construct directory path based on the expected structure
        sample_dir = os.path.join(
            results_dir, 
            "analysis_nanopore", 
            "ecdna", 
            "decoil", 
            sample_name
        )
    
    summary_file = os.path.join(sample_dir, "summary.txt")
    bed_file = os.path.join(sample_dir, "reconstruct.ecDNA.filtered.bed")
    
    print(f"Processing sample: {sample_name}")
    print(f"  Directory: {sample_dir}")
    print(f"  Summary file: {summary_file}")
    print(f"  BED file: {bed_file}")
    
    # Load the files
    summary_df = load_summary_file(summary_file)
    bed_df = load_bed_file(bed_file)
    
    if summary_df.empty and bed_df.empty:
        print(f"  No data found for sample {sample_name}")
        return pd.DataFrame()
    
    combined_data = []
    
    # Process summary data first to get basic info about each circular DNA
    if not summary_df.empty:
        for _, row in summary_df.iterrows():
            circ_id = row['circ_id']
            chr_origin = row.get('chr_origin', 'unknown')
            size_mb = row.get('size(MB)', 0)
            size_bp = size_mb * 1e6  # Convert MB to BP
            label = row.get('label', '')
            topology_name = row.get('topology_name', 'unknown')
            estimated_proportions = row.get('estimated_proportions', 0)
            
            # Find corresponding BED entries for this circ_id
            if not bed_df.empty:
                matching_bed = bed_df[bed_df['circ_id'] == circ_id]
                
                if not matching_bed.empty:
                    # Calculate region statistics
                    region_sizes = matching_bed['end'] - matching_bed['start']
                    max_region_size = region_sizes.max()
                    total_coverage = matching_bed['coverage'].sum()
                    mean_coverage = matching_bed['coverage'].mean()
                    num_regions = len(matching_bed)
                    
                    # Get chromosome and coordinate info
                    chromosomes = matching_bed['chr'].unique()
                    chr_list = ','.join(chromosomes)
                    
                    min_start = matching_bed['start'].min()
                    max_end = matching_bed['end'].max()
                    span_size = max_end - min_start
                else:
                    max_region_size = 0
                    total_coverage = 0
                    mean_coverage = 0
                    num_regions = 0
                    chr_list = chr_origin
                    span_size = size_bp
            else:
                max_region_size = size_bp
                total_coverage = estimated_proportions
                mean_coverage = estimated_proportions
                num_regions = 1
                chr_list = chr_origin
                span_size = size_bp
            
            # Determine if this is likely an ecDNA
            label_str = str(label).lower() if pd.notna(label) else ""
            topology_str = str(topology_name).lower() if pd.notna(topology_name) else ""
            is_ecdna = (label_str == 'ecdna' or
                       topology_str == 'multi_region_intra_chr' or
                       size_mb > 0.1)  # Arbitrary threshold for large circular DNA
            
            combined_data.append({
                'sample_name': sample_name,
                'circ_id': circ_id,
                'chromosomes': chr_list,
                'size_mb': size_mb,
                'size_bp': int(size_bp),
                'max_region_size_bp': int(max_region_size),
                'span_size_bp': int(span_size),
                'total_coverage': total_coverage,
                'mean_coverage': mean_coverage,
                'num_regions': num_regions,
                'label': label,
                'topology_name': topology_name,
                'estimated_proportions': estimated_proportions,
                'is_ecdna': is_ecdna
            })
    
    # If we only have BED data without summary, process BED entries
    elif not bed_df.empty:
        # Group by circ_id
        for circ_id, group in bed_df.groupby('circ_id'):
            region_sizes = group['end'] - group['start']
            max_region_size = region_sizes.max()
            total_size = region_sizes.sum()
            total_coverage = group['coverage'].sum()
            mean_coverage = group['coverage'].mean()
            num_regions = len(group)
            
            chromosomes = group['chr'].unique()
            chr_list = ','.join(chromosomes)
            
            min_start = group['start'].min()
            max_end = group['end'].max()
            span_size = max_end - min_start
            
            combined_data.append({
                'sample_name': sample_name,
                'circ_id': circ_id,
                'chromosomes': chr_list,
                'size_mb': total_size / 1e6,
                'size_bp': int(total_size),
                'max_region_size_bp': int(max_region_size),
                'span_size_bp': int(span_size),
                'total_coverage': total_coverage,
                'mean_coverage': mean_coverage,
                'num_regions': num_regions,
                'label': '',
                'topology_name': 'unknown',
                'estimated_proportions': mean_coverage,
                'is_ecdna': total_size > 100000  # 100kb threshold
            })
    
    result_df = pd.DataFrame(combined_data)
    print(f"  Found {len(result_df)} circular DNAs for sample {sample_name}")
    
    return result_df


def main():
    """Main function"""
    args = parse_arguments()
    
    print("=== Decoil Results Combiner ===")
    print(f"Results directory: {args.results_dir}")
    print(f"Output file: {args.output_file}")
    
    if args.test_mode:
        print("Running in TEST MODE")
        if not args.test_directory:
            print("Error: Test mode requires --test_directory")
            sys.exit(1)
        
        sample_list = ["RLGS2-primary"]  # Test with one sample
    else:
        sample_list = args.sample_list.split(',')
    
    print(f"Processing {len(sample_list)} samples: {sample_list}")
    
    all_results = []
    
    # Process each sample
    for sample in sample_list:
        sample = sample.strip()
        if args.test_mode:
            result_df = process_sample(
                sample, 
                args.results_dir, 
                test_mode=True,
                test_directory=args.test_directory
            )
        else:
            result_df = process_sample(sample, args.results_dir)
        
        if not result_df.empty:
            all_results.append(result_df)
    
    # Combine all results
    if all_results:
        final_df = pd.concat(all_results, ignore_index=True)
        
        # Sort by sample name and size
        final_df = final_df.sort_values(['sample_name', 'size_mb'], ascending=[True, False])
        
        # Create output directory if it doesn't exist
        output_dir = os.path.dirname(args.output_file)
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
        
        # Save results
        final_df.to_csv(args.output_file, sep='\t', index=False)
        
        print(f"\n=== SUMMARY ===")
        print(f"Total circular DNAs found: {len(final_df)}")
        print(f"Samples with circular DNAs: {final_df['sample_name'].nunique()}")
        print(f"Potential ecDNAs: {final_df['is_ecdna'].sum()}")
        print(f"Results saved to: {args.output_file}")
        
        # Print summary statistics
        if len(final_df) > 0:
            print(f"\nSize statistics:")
            print(f"  Mean size: {final_df['size_mb'].mean():.3f} MB")
            print(f"  Max size: {final_df['size_mb'].max():.3f} MB")
            print(f"  Min size: {final_df['size_mb'].min():.3f} MB")
            
            print(f"\nCoverage statistics:")
            print(f"  Mean coverage: {final_df['mean_coverage'].mean():.1f}")
            print(f"  Max coverage: {final_df['mean_coverage'].max():.1f}")
            print(f"  Min coverage: {final_df['mean_coverage'].min():.1f}")
    else:
        print("No results found for any samples!")
        # Create empty output file
        empty_df = pd.DataFrame(columns=[
            'sample_name', 'circ_id', 'chromosomes', 'size_mb', 'size_bp', 
            'max_region_size_bp', 'span_size_bp', 'total_coverage', 'mean_coverage', 
            'num_regions', 'label', 'topology_name', 'estimated_proportions', 'is_ecdna'
        ])
        empty_df.to_csv(args.output_file, sep='\t', index=False)


if __name__ == "__main__":
    main()
