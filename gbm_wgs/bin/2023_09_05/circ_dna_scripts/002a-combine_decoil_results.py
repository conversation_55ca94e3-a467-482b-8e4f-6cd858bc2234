# Alec Bahcheli
import pandas as pd
import os
import sys
import getopt
import time
import argparse

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--results_dir", required=True)  # Directory containing results
    parser.add_argument("--sample_list", required=True)  # Comma-separated list of samples
    parser.add_argument("--output_file", required=True)  # Output file path
    return parser.parse_args()

def load_bed_file(bed_path):
    """Load and process BED file with ecDNA regions"""
    if not os.path.exists(bed_path):
        return pd.DataFrame()
    
    try:
        bed_df = pd.read_csv(bed_path, sep='\t', header=None)
        if bed_df.empty:
            return pd.DataFrame()
        
        bed_df.columns = ['chr', 'start', 'end', 'name', 'score', 'strand', 
                         'thick_start', 'thick_end', 'color', 'coverage']
        return bed_df[['chr', 'start', 'end', 'name', 'coverage']]
    except:
        return pd.DataFrame()

def load_summary_file(summary_path):
    """Load and process summary file with ecDNA information"""
    if not os.path.exists(summary_path):
        return pd.DataFrame()
    
    try:
        with open(summary_path, 'r') as f:
            lines = f.readlines()
        
        data = []
        for line in lines:
            if line.startswith('ecDNA'):
                parts = line.strip().split('\t')
                if len(parts) >= 4:
                    circ_id = parts[0]
                    size_bp = int(parts[1])
                    topology = parts[2]
                    is_ecdna = topology == 'circular'
                    data.append([circ_id, size_bp, topology, is_ecdna])
        
        if data:
            return pd.DataFrame(data, columns=['circ_id', 'size_bp', 'topology', 'is_ecdna'])
        return pd.DataFrame()
    except:
        return pd.DataFrame()

def process_sample(sample_name, results_dir):
    """Process a single sample and return combined data"""
    sample_dir = os.path.join(results_dir, "analysis_nanopore", "ecdna", "decoil", sample_name)
    
    bed_path = os.path.join(sample_dir, "reconstruct.ecDNA.filtered.bed")
    summary_path = os.path.join(sample_dir, "summary.txt")
    
    bed_df = load_bed_file(bed_path)
    summary_df = load_summary_file(summary_path)
    
    if bed_df.empty and summary_df.empty:
        return pd.DataFrame()
    
    # Merge bed and summary data
    if not bed_df.empty and not summary_df.empty:
        merged_df = pd.merge(bed_df, summary_df, on='circ_id', how='outer')
    elif not bed_df.empty:
        merged_df = bed_df.copy()
        merged_df['size_bp'] = merged_df['end'] - merged_df['start']
        merged_df['is_ecdna'] = True
    else:
        merged_df = summary_df.copy()
        merged_df['coverage'] = 0
    
    # Add sample information
    merged_df['sample_name'] = sample_name
    merged_df['size_mb'] = merged_df['size_bp'] / 1_000_000
    merged_df['mean_coverage'] = merged_df.get('coverage', 0)
    
    return merged_df

def main():
    """Main function to process all samples and combine results"""
    args = parse_arguments()
    sample_list = args.sample_list.split(',')
    all_results = []
    
    for sample in sample_list:
        sample = sample.strip()
        result_df = process_sample(sample, args.results_dir)
        
        if not result_df.empty:
            all_results.append(result_df)
    
    if all_results:
        final_df = pd.concat(all_results, ignore_index=True)
        final_df = final_df.sort_values(['sample_name', 'size_mb'], ascending=[True, False])
        
        os.makedirs(os.path.dirname(args.output_file), exist_ok=True)
        final_df.to_csv(args.output_file, sep='\t', index=False)
    else:
        # Create empty file with headers
        empty_df = pd.DataFrame(columns=['circ_id', 'chr', 'start', 'end', 'coverage', 
                                       'size_bp', 'topology', 'is_ecdna', 'sample_name', 
                                       'size_mb', 'mean_coverage'])
        empty_df.to_csv(args.output_file, sep='\t', index=False)

if __name__ == "__main__":
    main()
