# <PERSON>cheli
import argparse
import pandas as pd
import os

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--results_dir", required=True)  # Directory containing results
    parser.add_argument("--sample_list", required=True)  # Comma-separated list of samples
    parser.add_argument("--combined_results", required=True)  # Output file path
    parser.add_argument("--summary_results", required=True)  # Output file path
    parser.add_argument("--ecdna_counts", required=True)  # Output file path
    return parser.parse_args()


def process_sample(sample_name, results_dir):
    sample_dir = os.path.join(results_dir, "analysis_nanopore", "ecdna", "decoil", sample_name)
    
    bed_path = os.path.join(sample_dir, "reconstruct.ecDNA.filtered.bed")
    summary_path = os.path.join(sample_dir, "summary.txt")
    
    # load bed file
    bed_df = pd.read_csv(bed_path, sep='\t')
    bed_df = bed_df.rename(columns={'#chr':'chr', 'estimated_proportions':'copy_number'})
    
    # load summary file
    summary_df = pd.read_csv(summary_path, sep='\t')

    # subset to only ecDNA
    summary_df = summary_df[summary_df['label'] == 'ecDNA']
    summary_df['sample_name'] = sample_name

    # check if empty
    if summary_df.empty:
        # create an empty df with the sample name
        summary_df = pd.DataFrame([{
            'size(MB)': 0,
            'sample_name': sample_name
        }])
        return summary_df, summary_df

    # Merge bed and summary data
    merged_df = pd.merge(bed_df, summary_df.copy(), on='circ_id', how='outer')
    
    # Add sample information
    merged_df['size_bp'] = merged_df['size(MB)'] * 1_000_000
    merged_df['mean_coverage'] = merged_df.get('coverage', 0)

    # remove unnecessary cols
    merged_df = merged_df[['circ_id', 'chr', 'start', 'end', 'coverage', 'size_bp', 'copy_number', 'sample_name']]
    
    return merged_df, summary_df


def load_and_combine_ecdnas(sample_list, results_dir):
    """Load and combine ecDNA data from all samples"""
    all_results = []
    summary_dfs = []
    
    # Process each sample
    for sample in sample_list:
        result_df, summary_df = process_sample(sample, results_dir)
        all_results.append(result_df)
        summary_dfs.append(summary_df)
    
    # Combine and sort results
    final_df = pd.concat(all_results, ignore_index=True)
    final_df = final_df.sort_values(['sample_name', 'size_bp'], ascending=[True, False])
    
    # Combine summary results
    summary_df = pd.concat(summary_dfs, ignore_index=True).fillna(0)
    summary_df['patient'] = summary_df['sample_name'].str.split('-').str[0]
    summary_df['tumor_type'] = summary_df['sample_name'].str.split('-').str[1]
    summary_df['size_bp'] = summary_df['size(MB)'] * 1_000_000

    # add patient and tumor type
    final_df['patient'] = final_df['sample_name'].str.split('-').str[0]
    final_df['tumor_type'] = final_df['sample_name'].str.split('-').str[1]

    return final_df, summary_df


def create_summary_df(summary_dfs):
    # create summary df with counts, setting samples with only zero size to 0
    counts = summary_dfs.groupby('sample_name').apply(lambda x: (x['size(MB)'] != 0).sum()).reset_index(name='n_ecdnas')
    summary_df = counts

    return summary_df

def main():
    """Main function to process all samples and combine results"""
    args = parse_arguments()
    sample_list = args.sample_list.split(',')

    # load and combine ecDNAs
    final_df, summary_df = load_and_combine_ecdnas(sample_list, args.results_dir)

    # create summary df
    counts_df = create_summary_df(summary_df)

    # save results
    final_df.to_csv(args.combined_results, sep='\t', index=False)
    summary_df.to_csv(args.summary_results, sep='\t', index=False)
    counts_df.to_csv(args.ecdna_counts, sep='\t', index=False)


if __name__ == "__main__":
    main()
