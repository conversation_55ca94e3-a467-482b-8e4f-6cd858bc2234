#!/usr/bin/env Rscript

# Load required packages while suppressing startup messages
suppressPackageStartupMessages({
  library(ggplot2)      # For creating plots and themes
  library(dplyr)        # For data manipulation
  library(tidyr)        # For data tidying
  library(readr)        # For reading TSV files
  library(RColorBrewer) # For color palettes
  library(optparse)     # For parsing command line options
})

# Define command line options
option_list <- list(
  make_option(c("-i", "--input_file"), type="character", help="Path to input TSV file"),
  make_option(c("-o", "--figure_file"), type="character", help="Prefix for output files")
)

# Parse command line arguments
opt_parser <- OptionParser(option_list=option_list)
opt <- parse_args(opt_parser)

plot_theme = function(...) {
    theme_bw() +
    theme(    
        plot.title = element_text(size = 22),
        plot.caption = element_text(size = 12),
        plot.subtitle = element_text(size = 16),
        axis.title = element_text(size = 18),
        axis.text.x = element_text(size = 12,
                angle = 90, hjust = 1, vjust=0.5, color = "black"),
        axis.text.y = element_text(size = 12, color = "black"),
        legend.title = element_text(size = 16),
        legend.text = element_text(size = 14),
        ...
    )
}

# Check if required arguments are provided
if (is.null(opt$input_file) || is.null(opt$figure_file)) {
  print_help(opt_parser)
  stop("Both input_file and figure_file arguments must be supplied.")
}

# Read the input data
raw_data <- read_tsv(opt$input_file, show_col_types = FALSE)

# Process the data:
# - Extract patient ID from before the hyphen in sample_name
# - Extract tumor type from after the hyphen in sample_name
# - Convert size from base pairs to megabases
data <- raw_data %>%
  mutate(
    patient = gsub("-.*", "", sample_name),
    tumor_type = gsub(".*-", "", sample_name),
    size_mb = size_bp / 1e6
  )

# Create color schemes for patients and tumor types
patient_colors <- rainbow(length(unique(data$patient)))  # Unique color for each patient
names(patient_colors) <- unique(data$patient)
tumor_colors <- c("primary" = "#E31A1C", "recurrent" = "#1F78B4")  # Red for primary, blue for recurrent

# Create scatter plot of amplicon size vs median copy number
p1 <- ggplot(data, aes(x = size_mb, y = median_copy_number, 
                      color = patient, shape = tumor_type)) + plot_theme() +
  geom_point(size = 3, alpha = 0.8) + # Add points with some transparency
  scale_color_manual(values = patient_colors, name = "Patient") +  # Set patient-specific colors
  scale_shape_manual(values = c("primary" = 16, "recurrent" = 17),  # Different shapes for tumor types
                    name = "Tumor Type") +
  labs(title = "Amplicon Size vs Median Copy Number",
       x = "Size (Mb)", y = "Median Copy Number") +
  theme(axis.text.x = element_text(angle = 0))  # ← Fix is here

# Create count plot of number of amplicons per sample
amplicon_counts <- raw_data %>%
  group_by(sample_name) %>%
  summarise(count = n())

p2 <- ggplot(amplicon_counts, aes(x = sample_name, y = count)) + plot_theme() +
  geom_bar(stat = "identity") +
  labs(title = "Number of Amplicons per Sample",
       x = "Sample", y = "Count") +
  theme(axis.text.x = element_text(angle = 45, hjust = 1, vjust = 1))  # ← Fix is here

# Create plot of canonical oncogenes per sample-amplicon
oncogene_data <- raw_data %>%
  mutate(
    oncogene_count = ifelse(!is.na(canonical_oncogenes), 
                           lengths(strsplit(canonical_oncogenes, ",")), 
                           0),
    sample_label = paste0(sample_name, "_", sapply(strsplit(amplicon_name, "_"), `[`, 1))  # Add first part before "_"
  ) %>%
  group_by(sample_label, amplicon_name) %>%
  summarise(
    oncogene_count = sum(oncogene_count),
    oncogene_names = paste(na.omit(canonical_oncogenes), collapse=", "),
    .groups = 'drop'
  )

p3 <- ggplot(oncogene_data, aes(x = sample_label, y = oncogene_count)) + plot_theme() +
  geom_bar(stat = "identity") +
  geom_text(aes(label = oncogene_names), vjust = -0.5, size = 2) +  # Oncogene labels above bars
  labs(title = "Canonical Oncogenes per Sample-Amplicon",
       x = "Sample", y = "Count of Oncogenes") +
  theme(axis.text.x = element_text(angle = 45, hjust = 1, vjust = 1))  # ← Fix is here

# Save plots to PDF
pdf(opt$figure_file)
print(p1)
print(p2)
print(p3)
dev.off()
