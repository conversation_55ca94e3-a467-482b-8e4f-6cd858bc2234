#!/usr/bin/env Rscript
# Script to visualize AmpliconSuite results
# Author: <PERSON>
# Date: 2023-09-05

# Load required libraries
suppressPackageStartupMessages({
  library(ggplot2)
  library(dplyr)
  library(tidyr)
  library(readr)
  library(scales)
  library(RColorBrewer)
  library(viridis)
  library(patchwork)
})

# Parse command line arguments
args <- commandArgs(trailingOnly = TRUE)

if (length(args) < 2) {
  cat("Usage: Rscript 001b-visualize_amplicon_results.R <input_file> <output_prefix>\n")
  cat("  input_file: Combined amplicon results TSV file\n")
  cat("  output_prefix: Prefix for output files (without extension)\n")
  quit(status = 1)
}

input_file <- args[1]
output_prefix <- args[2]

cat("=== AmpliconSuite Results Visualizer ===\n")
cat("Input file:", input_file, "\n")
cat("Output prefix:", output_prefix, "\n")

# Check if input file exists
if (!file.exists(input_file)) {
  cat("Error: Input file does not exist:", input_file, "\n")
  quit(status = 1)
}

# Load data
cat("Loading data...\n")
data <- read_tsv(input_file, show_col_types = FALSE)

cat("Data dimensions:", nrow(data), "rows,", ncol(data), "columns\n")

# Check if data is empty
if (nrow(data) == 0) {
  cat("Warning: No data found in input file. Creating empty plots.\n")
  
  # Create empty plot
  empty_plot <- ggplot() + 
    geom_text(aes(x = 0.5, y = 0.5, label = "No amplicons detected"), 
              size = 6, hjust = 0.5, vjust = 0.5) +
    xlim(0, 1) + ylim(0, 1) +
    theme_void() +
    labs(title = "AmpliconSuite Results Summary",
         subtitle = "No amplicons detected in any samples")
  
  ggsave(paste0(output_prefix, "_summary.pdf"), empty_plot, 
         width = 8, height = 6, units = "in")
  
  cat("Empty summary plot saved to:", paste0(output_prefix, "_summary.pdf"), "\n")
  quit(status = 0)
}

# Print data summary
cat("\nData Summary:\n")
cat("Samples:", length(unique(data$sample_name)), "\n")
cat("Total amplicons:", nrow(data), "\n")
cat("Copy number range:", min(data$median_copy_number, na.rm = TRUE), "-", 
    max(data$median_copy_number, na.rm = TRUE), "\n")
cat("Size range:", min(data$size_bp, na.rm = TRUE), "-", 
    max(data$size_bp, na.rm = TRUE), "bp\n")

# Prepare data for plotting
data <- data %>%
  mutate(
    patient = gsub("-.*", "", sample_name),
    tumor_type = gsub(".*-", "", sample_name),
    size_mb = size_bp / 1e6,
    log10_size = log10(size_bp + 1),
    has_oncogenes = canonical_oncogenes != "None" & canonical_oncogenes != ""
  )

# Color palettes
patient_colors <- rainbow(length(unique(data$patient)))
names(patient_colors) <- unique(data$patient)

tumor_colors <- c("primary" = "#E31A1C", "recurrent" = "#1F78B4")

# 1. Amplicon counts per sample
cat("Creating amplicon count plot...\n")
count_data <- data %>%
  group_by(sample_name, patient, tumor_type) %>%
  summarise(n_amplicons = n(), .groups = "drop")

p1 <- ggplot(count_data, aes(x = reorder(sample_name, n_amplicons), 
                             y = n_amplicons, fill = tumor_type)) +
  geom_col(alpha = 0.8) +
  scale_fill_manual(values = tumor_colors, name = "Tumor Type") +
  coord_flip() +
  labs(title = "Number of Amplicons per Sample",
       x = "Sample", y = "Number of Amplicons") +
  theme_minimal() +
  theme(axis.text.y = element_text(size = 8))

# 2. Copy number distribution
cat("Creating copy number distribution plot...\n")
p2 <- ggplot(data, aes(x = median_copy_number, fill = tumor_type)) +
  geom_histogram(bins = 20, alpha = 0.7, position = "identity") +
  scale_fill_manual(values = tumor_colors, name = "Tumor Type") +
  scale_x_continuous(trans = "log10", labels = scales::comma) +
  labs(title = "Distribution of Median Copy Numbers",
       x = "Median Copy Number (log10 scale)", y = "Count") +
  theme_minimal()

# 3. Size distribution
cat("Creating size distribution plot...\n")
p3 <- ggplot(data, aes(x = size_mb, fill = tumor_type)) +
  geom_histogram(bins = 20, alpha = 0.7, position = "identity") +
  scale_fill_manual(values = tumor_colors, name = "Tumor Type") +
  labs(title = "Distribution of Amplicon Sizes",
       x = "Size (Mb)", y = "Count") +
  theme_minimal()

# 4. Copy number vs Size scatter plot
cat("Creating copy number vs size scatter plot...\n")
p4 <- ggplot(data, aes(x = size_mb, y = median_copy_number, 
                       color = patient, shape = tumor_type)) +
  geom_point(size = 3, alpha = 0.8) +
  scale_color_manual(values = patient_colors, name = "Patient") +
  scale_shape_manual(values = c("primary" = 16, "recurrent" = 17), 
                     name = "Tumor Type") +
  scale_y_continuous(trans = "log10", labels = scales::comma) +
  labs(title = "Amplicon Size vs Copy Number",
       x = "Size (Mb)", y = "Median Copy Number (log10 scale)") +
  theme_minimal()

# 5. Oncogene presence
if (any(data$has_oncogenes)) {
  cat("Creating oncogene analysis plot...\n")
  oncogene_data <- data %>%
    group_by(sample_name, tumor_type) %>%
    summarise(
      total_amplicons = n(),
      amplicons_with_oncogenes = sum(has_oncogenes),
      prop_with_oncogenes = amplicons_with_oncogenes / total_amplicons,
      .groups = "drop"
    )
  
  p5 <- ggplot(oncogene_data, aes(x = reorder(sample_name, prop_with_oncogenes), 
                                  y = prop_with_oncogenes, fill = tumor_type)) +
    geom_col(alpha = 0.8) +
    scale_fill_manual(values = tumor_colors, name = "Tumor Type") +
    scale_y_continuous(labels = scales::percent) +
    coord_flip() +
    labs(title = "Proportion of Amplicons with Canonical Oncogenes",
         x = "Sample", y = "Proportion with Oncogenes") +
    theme_minimal() +
    theme(axis.text.y = element_text(size = 8))
} else {
  p5 <- ggplot() + 
    geom_text(aes(x = 0.5, y = 0.5, label = "No canonical oncogenes detected"), 
              size = 4) +
    xlim(0, 1) + ylim(0, 1) +
    theme_void() +
    labs(title = "Canonical Oncogenes in Amplicons")
}

# 6. Summary statistics table plot
cat("Creating summary statistics...\n")
summary_stats <- data %>%
  group_by(tumor_type) %>%
  summarise(
    n_samples = n_distinct(sample_name),
    n_amplicons = n(),
    mean_copy_number = mean(median_copy_number, na.rm = TRUE),
    median_copy_number = median(median_copy_number, na.rm = TRUE),
    mean_size_mb = mean(size_mb, na.rm = TRUE),
    median_size_mb = median(size_mb, na.rm = TRUE),
    .groups = "drop"
  ) %>%
  mutate(across(where(is.numeric), ~round(.x, 2)))

# Convert to long format for plotting
summary_long <- summary_stats %>%
  pivot_longer(cols = -tumor_type, names_to = "metric", values_to = "value") %>%
  mutate(
    metric_label = case_when(
      metric == "n_samples" ~ "Number of Samples",
      metric == "n_amplicons" ~ "Number of Amplicons",
      metric == "mean_copy_number" ~ "Mean Copy Number",
      metric == "median_copy_number" ~ "Median Copy Number",
      metric == "mean_size_mb" ~ "Mean Size (Mb)",
      metric == "median_size_mb" ~ "Median Size (Mb)",
      TRUE ~ metric
    )
  )

p6 <- ggplot(summary_long, aes(x = metric_label, y = value, fill = tumor_type)) +
  geom_col(position = "dodge", alpha = 0.8) +
  scale_fill_manual(values = tumor_colors, name = "Tumor Type") +
  coord_flip() +
  labs(title = "Summary Statistics by Tumor Type",
       x = "Metric", y = "Value") +
  theme_minimal() +
  theme(axis.text.y = element_text(size = 10))

# Combine plots
cat("Combining plots...\n")
combined_plot <- (p1 + p2) / (p3 + p4) / (p5 + p6)
combined_plot <- combined_plot + 
  plot_annotation(
    title = "AmpliconSuite Results Summary",
    subtitle = paste("Analysis of", nrow(data), "amplicons from", 
                     length(unique(data$sample_name)), "samples"),
    theme = theme(plot.title = element_text(size = 16, hjust = 0.5),
                  plot.subtitle = element_text(size = 12, hjust = 0.5))
  )

# Save plots
cat("Saving plots...\n")

# Combined plot
ggsave(paste0(output_prefix, "_summary.pdf"), combined_plot, 
       width = 16, height = 20, units = "in")

# Individual plots
ggsave(paste0(output_prefix, "_counts.pdf"), p1, 
       width = 10, height = 8, units = "in")
ggsave(paste0(output_prefix, "_copy_numbers.pdf"), p2, 
       width = 8, height = 6, units = "in")
ggsave(paste0(output_prefix, "_sizes.pdf"), p3, 
       width = 8, height = 6, units = "in")
ggsave(paste0(output_prefix, "_scatter.pdf"), p4, 
       width = 10, height = 8, units = "in")
ggsave(paste0(output_prefix, "_oncogenes.pdf"), p5, 
       width = 10, height = 8, units = "in")
ggsave(paste0(output_prefix, "_statistics.pdf"), p6, 
       width = 10, height = 6, units = "in")

# Save summary table
write_tsv(summary_stats, paste0(output_prefix, "_summary_stats.tsv"))

cat("\n=== VISUALIZATION COMPLETE ===\n")
cat("Files created:\n")
cat("  Combined plot:", paste0(output_prefix, "_summary.pdf"), "\n")
cat("  Individual plots:", paste0(output_prefix, "_*.pdf"), "\n")
cat("  Summary statistics:", paste0(output_prefix, "_summary_stats.tsv"), "\n")

cat("\nDone!\n")
