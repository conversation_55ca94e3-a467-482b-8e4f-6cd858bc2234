#!/usr/bin/env python3
"""
<PERSON>ript to combine AmpliconSuite results from multiple samples
Author: <PERSON>
Date: 2023-09-05

This script combines the feature_basic_properties.tsv and gene_list.tsv files
from AmpliconSuite/AmpliconClassifier results into a single summary file.

Input files:
- {patient}-{tumor}_feature_basic_properties.tsv: Contains amplicon properties
- {patient}-{tumor}_gene_list.tsv: Contains gene information for each amplicon

Output:
- Combined TSV file with amplicon name, sample name, main genes (CSV), and mean copy number
"""

import pandas as pd
import argparse
import os
import sys
from pathlib import Path


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Combine AmpliconSuite results from multiple samples"
    )
    parser.add_argument(
        "--results_dir",
        required=True,
        help="Base results directory containing circ_dna subdirectories"
    )
    parser.add_argument(
        "--sample_list",
        required=True,
        help="Comma-separated list of samples (e.g., RLGS1-primary,RLGS2-primary)"
    )
    parser.add_argument(
        "--output_file",
        required=True,
        help="Output TSV file path"
    )
    parser.add_argument(
        "--test_mode",
        action="store_true",
        help="Test mode using example files"
    )
    parser.add_argument(
        "--test_basic_properties",
        help="Path to test basic properties file"
    )
    parser.add_argument(
        "--test_gene_list",
        help="Path to test gene list file"
    )
    
    return parser.parse_args()


def load_basic_properties(file_path):
    """Load and process basic properties file"""
    if not os.path.exists(file_path):
        print(f"Warning: Basic properties file not found: {file_path}")
        return pd.DataFrame()
    
    try:
        df = pd.read_csv(file_path, sep='\t')
        print(f"Loaded basic properties: {file_path} ({len(df)} rows)")
        return df
    except Exception as e:
        print(f"Error loading basic properties file {file_path}: {e}")
        return pd.DataFrame()


def load_gene_list(file_path):
    """Load and process gene list file"""
    if not os.path.exists(file_path):
        print(f"Warning: Gene list file not found: {file_path}")
        return pd.DataFrame()
    
    try:
        df = pd.read_csv(file_path, sep='\t')
        print(f"Loaded gene list: {file_path} ({len(df)} rows)")
        return df
    except Exception as e:
        print(f"Error loading gene list file {file_path}: {e}")
        return pd.DataFrame()


def extract_amplicon_info(feature_id):
    """Extract amplicon information from feature_ID"""
    # Example: RLGS2-primary_amplicon1_ecDNA_1
    parts = feature_id.split('_')
    if len(parts) >= 3:
        sample_name = parts[0]  # RLGS2-primary
        amplicon_name = '_'.join(parts[1:])  # amplicon1_ecDNA_1
        return sample_name, amplicon_name
    else:
        return feature_id, feature_id


def process_sample(sample_name, results_dir, test_mode=False, test_basic_properties=None, test_gene_list=None):
    """Process a single sample and return combined data"""
    
    if test_mode:
        basic_properties_file = test_basic_properties
        gene_list_file = test_gene_list
        sample_name = "RLGS2-primary"  # Use the test sample name
    else:
        # Construct file paths based on the expected structure
        basic_properties_file = os.path.join(
            results_dir, 
            "circ_dna", 
            sample_name, 
            "ampliconsuite", 
            "ampliconclassifier", 
            f"{sample_name}_feature_basic_properties.tsv"
        )
        gene_list_file = os.path.join(
            results_dir, 
            "circ_dna", 
            sample_name, 
            "ampliconsuite", 
            "ampliconclassifier", 
            f"{sample_name}_gene_list.tsv"
        )
    
    print(f"Processing sample: {sample_name}")
    print(f"  Basic properties: {basic_properties_file}")
    print(f"  Gene list: {gene_list_file}")
    
    # Load the files
    basic_props = load_basic_properties(basic_properties_file)
    gene_list = load_gene_list(gene_list_file)
    
    if basic_props.empty and gene_list.empty:
        print(f"  No data found for sample {sample_name}")
        return pd.DataFrame()
    
    combined_data = []
    
    # Process each amplicon in basic properties
    for _, row in basic_props.iterrows():
        feature_id = row['feature_ID']
        sample_from_feature, amplicon_name = extract_amplicon_info(feature_id)
        
        # Get basic properties
        size_bp = row.get('captured_region_size_bp', 0)
        median_cn = row.get('median_feature_CN', 0)
        max_cn = row.get('max_feature_CN', 0)
        
        # Find corresponding genes
        if not gene_list.empty:
            # Match by feature_ID or by amplicon pattern
            matching_genes = gene_list[
                (gene_list['sample_name'] == sample_name) & 
                (gene_list['feature'] == amplicon_name.split('_')[-1])  # e.g., "1" from "ecDNA_1"
            ]
            
            if matching_genes.empty:
                # Try alternative matching
                matching_genes = gene_list[gene_list['sample_name'] == sample_name]
            
            if not matching_genes.empty:
                # Get unique genes and their copy numbers
                genes_info = matching_genes[['gene', 'gene_cn', 'is_canonical_oncogene']].drop_duplicates()
                
                # Create gene list with copy numbers
                gene_strings = []
                oncogenes = []
                for _, gene_row in genes_info.iterrows():
                    gene_name = gene_row['gene']
                    gene_cn = gene_row['gene_cn']
                    is_oncogene = gene_row.get('is_canonical_oncogene', False)
                    
                    gene_strings.append(f"{gene_name}({gene_cn:.1f})")
                    if is_oncogene:
                        oncogenes.append(gene_name)
                
                main_genes = ','.join(gene_strings)
                canonical_oncogenes = ','.join(oncogenes) if oncogenes else 'None'
            else:
                main_genes = 'No genes found'
                canonical_oncogenes = 'None'
        else:
            main_genes = 'No gene data'
            canonical_oncogenes = 'None'
        
        # Add to combined data
        combined_data.append({
            'sample_name': sample_name,
            'amplicon_name': amplicon_name,
            'feature_id': feature_id,
            'size_bp': size_bp,
            'median_copy_number': median_cn,
            'max_copy_number': max_cn,
            'main_genes': main_genes,
            'canonical_oncogenes': canonical_oncogenes,
            'num_genes': len(main_genes.split(',')) if main_genes != 'No genes found' and main_genes != 'No gene data' else 0
        })
    
    result_df = pd.DataFrame(combined_data)
    print(f"  Found {len(result_df)} amplicons for sample {sample_name}")
    
    return result_df


def main():
    """Main function"""
    args = parse_arguments()
    
    print("=== AmpliconSuite Results Combiner ===")
    print(f"Results directory: {args.results_dir}")
    print(f"Output file: {args.output_file}")
    
    if args.test_mode:
        print("Running in TEST MODE")
        if not args.test_basic_properties or not args.test_gene_list:
            print("Error: Test mode requires --test_basic_properties and --test_gene_list")
            sys.exit(1)
        
        sample_list = ["RLGS2-primary"]  # Test with one sample
    else:
        sample_list = args.sample_list.split(',')
    
    print(f"Processing {len(sample_list)} samples: {sample_list}")
    
    all_results = []
    
    # Process each sample
    for sample in sample_list:
        sample = sample.strip()
        if args.test_mode:
            result_df = process_sample(
                sample, 
                args.results_dir, 
                test_mode=True,
                test_basic_properties=args.test_basic_properties,
                test_gene_list=args.test_gene_list
            )
        else:
            result_df = process_sample(sample, args.results_dir)
        
        if not result_df.empty:
            all_results.append(result_df)
    
    # Combine all results
    if all_results:
        final_df = pd.concat(all_results, ignore_index=True)
        
        # Sort by sample name and copy number
        final_df = final_df.sort_values(['sample_name', 'median_copy_number'], ascending=[True, False])
        
        # Create output directory if it doesn't exist
        output_dir = os.path.dirname(args.output_file)
        if output_dir:
            os.makedirs(output_dir, exist_ok=True)
        
        # Save results
        final_df.to_csv(args.output_file, sep='\t', index=False)
        
        print(f"\n=== SUMMARY ===")
        print(f"Total amplicons found: {len(final_df)}")
        print(f"Samples with amplicons: {final_df['sample_name'].nunique()}")
        print(f"Results saved to: {args.output_file}")
        
        # Print summary statistics
        if len(final_df) > 0:
            print(f"\nCopy number statistics:")
            print(f"  Mean median CN: {final_df['median_copy_number'].mean():.2f}")
            print(f"  Max median CN: {final_df['median_copy_number'].max():.2f}")
            print(f"  Min median CN: {final_df['median_copy_number'].min():.2f}")
            
            print(f"\nSize statistics:")
            print(f"  Mean size: {final_df['size_bp'].mean():.0f} bp")
            print(f"  Max size: {final_df['size_bp'].max():.0f} bp")
            print(f"  Min size: {final_df['size_bp'].min():.0f} bp")
    else:
        print("No results found for any samples!")
        # Create empty output file
        empty_df = pd.DataFrame(columns=[
            'sample_name', 'amplicon_name', 'feature_id', 'size_bp', 
            'median_copy_number', 'max_copy_number', 'main_genes', 
            'canonical_oncogenes', 'num_genes'
        ])
        empty_df.to_csv(args.output_file, sep='\t', index=False)


if __name__ == "__main__":
    main()
