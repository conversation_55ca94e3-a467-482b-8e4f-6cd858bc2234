#!/usr/bin/env python3
"""
<PERSON>ript to combine AmpliconSuite results from multiple samples
Author: <PERSON>
Date: 2023-09-05
"""

# Import required libraries
import pandas as pd
import argparse
import os


def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--results_dir", required=True)  # Directory containing results
    parser.add_argument("--sample_list", required=True)  # Comma-separated list of samples
    parser.add_argument("--output_file", required=True)  # Output file path
    return parser.parse_args()


def extract_amplicon_info(feature_id):
    """Extract sample name and amplicon name from feature ID"""
    parts = feature_id.split('_')
    if len(parts) >= 3:
        return parts[0], '_'.join(parts[1:])
    return feature_id, feature_id


def process_sample(sample_name, results_dir):
    """Process a single sample's amplicon results"""
    # define the files to load
    basic_props_file = os.path.join(
        results_dir, "circ_dna", sample_name, "ampliconsuite",
        "ampliconclassifier", f"{sample_name}_feature_basic_properties.tsv"
    )
    gene_list_file = os.path.join(
        results_dir, "circ_dna", sample_name, "ampliconsuite",
        "ampliconclassifier", f"{sample_name}_gene_list.tsv"
    )

    # load the files
    basic_props = pd.read_csv(basic_props_file, sep='\t')
    gene_list = pd.read_csv(gene_list_file, sep='\t')
    
    combined_data = []
    # Process each feature in the basic properties file
    for _, row in basic_props.iterrows():
        feature_id = row['feature_ID']
        sample_from_feature, amplicon_name = extract_amplicon_info(feature_id)
        
        # Extract copy number and size information
        size_bp = row.get('captured_region_size_bp', 0)
        median_cn = row.get('median_feature_CN', 0)
        max_cn = row.get('max_feature_CN', 0)
        
        # Process gene information if available
        if not gene_list.empty and sample_from_feature == sample_name:
            # Find genes matching this amplicon
            matching_genes = gene_list[
                (gene_list['sample_name'] == sample_name) & 
                (gene_list['feature'] == amplicon_name.split('_')[-1])
            ]
            
            # If no specific matches, get all genes for this sample
            if matching_genes.empty:
                matching_genes = gene_list[gene_list['sample_name'] == sample_name]
            
            if not matching_genes.empty:
                # Process gene information and create formatted strings
                genes_info = matching_genes[['gene', 'gene_cn', 'is_canonical_oncogene']].drop_duplicates()
                gene_strings = []
                oncogenes = []
                for _, gene_row in genes_info.iterrows():
                    gene_name = gene_row['gene']
                    gene_cn = gene_row['gene_cn']
                    is_oncogene = gene_row.get('is_canonical_oncogene', False)
                    gene_strings.append(f"{gene_name}({gene_cn:.1f})")
                    if is_oncogene:
                        oncogenes.append(gene_name)
                
                main_genes = ','.join(gene_strings)
                canonical_oncogenes = ','.join(oncogenes) if oncogenes else 'None'
            else:
                main_genes = 'No genes found'
                canonical_oncogenes = 'None'
        else:
            main_genes = 'No gene data'
            canonical_oncogenes = 'None'
        
        # Combine all information into a dictionary
        combined_data.append({
            'sample_name': sample_name,
            'amplicon_name': amplicon_name,
            'feature_id': feature_id,
            'size_bp': size_bp,
            'median_copy_number': median_cn,
            'max_copy_number': max_cn,
            'main_genes': main_genes,
            'canonical_oncogenes': canonical_oncogenes,
            'num_genes': len(main_genes.split(',')) if main_genes not in ['No genes found', 'No gene data'] else 0
        })
    
    return pd.DataFrame(combined_data)


def main():
    """Main function to process all samples and combine results"""
    args = parse_arguments()
    sample_list = args.sample_list.split(',')
    all_results = []
    
    # Process each sample
    for sample in sample_list:
        result_df = process_sample(sample.strip(), args.results_dir)
        all_results.append(result_df)
    
    # Combine and sort results
    final_df = pd.concat(all_results, ignore_index=True)
    final_df = final_df.sort_values(['sample_name', 'median_copy_number'], ascending=[True, False])
    # Create output directory if it doesn't exist and save results
    os.makedirs(os.path.dirname(args.output_file), exist_ok=True)
    final_df.to_csv(args.output_file, sep='\t', index=False)


if __name__ == "__main__":
    main()
