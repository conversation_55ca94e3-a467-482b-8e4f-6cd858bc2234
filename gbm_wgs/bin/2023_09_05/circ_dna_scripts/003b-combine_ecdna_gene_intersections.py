# <PERSON> Bahcheli
import pandas as pd
import os
import sys
import argparse

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser(
        description="Combine ecDNA-gene intersection results from multiple samples"
    )
    parser.add_argument(
        "--input_files",
        required=True,
        help="Comma-separated list of input TSV files"
    )
    parser.add_argument(
        "--output_file",
        required=True,
        help="Output combined TSV file"
    )
    
    return parser.parse_args()

def load_intersection_file(file_path):
    """Load a single intersection file"""
    if not os.path.exists(file_path):
        print(f"Warning: File not found: {file_path}")
        return pd.DataFrame()
    
    try:
        df = pd.read_csv(file_path, sep='\t')
        print(f"Loaded: {file_path} ({len(df)} intersections)")
        return df
    except Exception as e:
        print(f"Error loading {file_path}: {e}")
        return pd.DataFrame()

def generate_summary_stats(df, output_file):
    """Generate summary statistics for ecDNA-gene intersections"""
    if df.empty:
        # Create empty summary stats
        summary_stats = pd.DataFrame(columns=[
            'tumor_type', 'n_samples', 'n_ecdnas', 'n_genes', 'n_intersections',
            'mean_genes_per_ecdna', 'median_genes_per_ecdna',
            'mean_overlap_fraction', 'median_overlap_fraction'
        ])
    else:
        # Add tumor type information
        df_with_tumor = df.copy()
        df_with_tumor['tumor_type'] = df_with_tumor['sample_name'].str.extract(r'.*-(\w+)$')[0]
        
        # Calculate statistics by tumor type
        summary_list = []
        for tumor_type in df_with_tumor['tumor_type'].unique():
            tumor_data = df_with_tumor[df_with_tumor['tumor_type'] == tumor_type]
            
            # Basic counts
            n_samples = tumor_data['sample_name'].nunique()
            n_ecdnas = tumor_data.groupby('sample_name')['circ_id'].nunique().sum()
            n_genes = tumor_data['gene'].nunique()
            n_intersections = len(tumor_data)
            
            # Genes per ecDNA statistics
            genes_per_ecdna = tumor_data.groupby(['sample_name', 'circ_id'])['gene'].nunique()
            mean_genes_per_ecdna = genes_per_ecdna.mean()
            median_genes_per_ecdna = genes_per_ecdna.median()
            
            # Overlap fraction statistics
            mean_overlap_fraction = tumor_data['overlap_fraction'].mean()
            median_overlap_fraction = tumor_data['overlap_fraction'].median()
            
            summary_list.append({
                'tumor_type': tumor_type,
                'n_samples': n_samples,
                'n_ecdnas': n_ecdnas,
                'n_genes': n_genes,
                'n_intersections': n_intersections,
                'mean_genes_per_ecdna': round(mean_genes_per_ecdna, 2),
                'median_genes_per_ecdna': round(median_genes_per_ecdna, 2),
                'mean_overlap_fraction': round(mean_overlap_fraction, 3),
                'median_overlap_fraction': round(median_overlap_fraction, 3)
            })
        
        summary_stats = pd.DataFrame(summary_list)
    
    # Save summary statistics
    os.makedirs(os.path.dirname(output_file), exist_ok=True)
    summary_stats.to_csv(output_file, sep='\t', index=False)
    return summary_stats

def main():
    """Main function"""
    args = parse_arguments()
    
    print("=== ecDNA-Gene Intersection Combiner ===")
    print(f"Output file: {args.output_file}")
    
    # Parse input files
    input_files = [f.strip() for f in args.input_files.split(',')]
    print(f"Processing {len(input_files)} input files")
    
    all_results = []
    
    # Load each file
    for file_path in input_files:
        df = load_intersection_file(file_path)
        if not df.empty:
            all_results.append(df)
    
    # Combine all results
    if all_results:
        final_df = pd.concat(all_results, ignore_index=True)
        
        # Sort by sample name, circ_id, and gene
        final_df = final_df.sort_values(['sample_name', 'circ_id', 'gene'])
        
        # Create output directory if needed
        os.makedirs(os.path.dirname(args.output_file), exist_ok=True)
        
        # Save combined results
        final_df.to_csv(args.output_file, sep='\t', index=False)
        
        # Generate summary statistics
        summary_stats_file = args.output_file.replace('.tsv', '_summary_stats.tsv')
        summary_stats = generate_summary_stats(final_df, summary_stats_file)
        
        print(f"\n=== SUMMARY ===")
        print(f"Total intersections: {len(final_df)}")
        print(f"Samples: {final_df['sample_name'].nunique()}")
        print(f"Unique ecDNAs: {final_df.groupby('sample_name')['circ_id'].nunique().sum()}")
        print(f"Unique genes: {final_df['gene'].nunique()}")
        print(f"Results saved to: {args.output_file}")
        print(f"Summary statistics saved to: {summary_stats_file}")
        
        # Print top genes by frequency
        if len(final_df) > 0:
            print(f"\nTop 10 most frequent genes in ecDNAs:")
            gene_counts = final_df['gene'].value_counts().head(10)
            for gene, count in gene_counts.items():
                samples_with_gene = final_df[final_df['gene'] == gene]['sample_name'].nunique()
                print(f"  {gene}: {count} intersections in {samples_with_gene} samples")
    else:
        print("No intersection data found. Creating empty output files.")
        
        # Create empty combined file
        empty_df = pd.DataFrame(columns=[
            'chr', 'start', 'end', 'gene', 'circ_id', 'coverage',
            'gene_start', 'gene_end', 'gene_length', 'overlap_length', 
            'overlap_fraction', 'ecdna_start', 'ecdna_end', 'ecdna_length',
            'sample_name'
        ])
        empty_df.to_csv(args.output_file, sep='\t', index=False)
        
        # Create empty summary stats
        summary_stats_file = args.output_file.replace('.tsv', '_summary_stats.tsv')
        generate_summary_stats(empty_df, summary_stats_file)

if __name__ == "__main__":
    main()
