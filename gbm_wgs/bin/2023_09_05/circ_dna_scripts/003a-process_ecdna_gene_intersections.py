# Alec Bahcheli
import argparse
import pandas as pd
import os

def parse_arguments():
    """Parse command line arguments"""
    parser = argparse.ArgumentParser()
    parser.add_argument("--intersected_bed", required=True)  # Bedtools intersection output
    parser.add_argument("--ecdna_results", required=True)  # Combined ecDNA results
    parser.add_argument("--output_file", required=True)  # Output file path
    return parser.parse_args()

def load_intersection_results(intersected_bed_file):
    """Load bedtools intersection results"""
    if not os.path.exists(intersected_bed_file):
        print(f"Warning: Intersection file not found: {intersected_bed_file}")
        return pd.DataFrame()
    
    # Load bedtools intersection output
    # Format: gene_chr gene_start gene_end gene_name ... ecdna_chr ecdna_start ecdna_end ecdna_id_sample ecdna_coverage ecdna_strand
    try:
        df = pd.read_csv(intersected_bed_file, sep='\t', header=None)
        if df.empty:
            return pd.DataFrame()
        
        # Assign column names based on expected bedtools output
        # First columns are from genes BED file, last columns are from ecDNA BED
        gene_cols = ['gene_chr', 'gene_start', 'gene_end', 'gene_name']
        ecdna_cols = ['ecdna_chr', 'ecdna_start', 'ecdna_end', 'ecdna_id_sample', 'ecdna_coverage', 'ecdna_strand']
        
        # Adjust based on actual number of columns
        if len(df.columns) >= len(gene_cols) + len(ecdna_cols):
            df.columns = gene_cols + ['extra_' + str(i) for i in range(len(df.columns) - len(gene_cols) - len(ecdna_cols))] + ecdna_cols
        else:
            # Fallback column naming
            df.columns = ['col_' + str(i) for i in range(len(df.columns))]
        
        print(f"Loaded intersection results: {intersected_bed_file} ({len(df)} intersections)")
        return df
    except Exception as e:
        print(f"Error loading intersection file {intersected_bed_file}: {e}")
        return pd.DataFrame()

def load_ecdna_results(ecdna_results_file):
    """Load combined ecDNA results"""
    try:
        df = pd.read_csv(ecdna_results_file, sep='\t')
        print(f"Loaded ecDNA results: {ecdna_results_file} ({len(df)} regions)")
        return df
    except Exception as e:
        print(f"Error loading ecDNA results {ecdna_results_file}: {e}")
        return pd.DataFrame()

def process_intersections(intersection_df, ecdna_df):
    """Process intersection results to create gene-ecDNA associations"""
    if intersection_df.empty or ecdna_df.empty:
        return pd.DataFrame()
    
    # Extract circ_id and sample from ecdna_id_sample column
    intersection_df[['circ_id', 'sample_name']] = intersection_df['ecdna_id_sample'].str.split('_', n=1, expand=True)
    intersection_df['circ_id'] = pd.to_numeric(intersection_df['circ_id'], errors='coerce')
    
    # Create patient column
    intersection_df['patient'] = intersection_df['sample_name'].str.split('-').str[0]
    
    # Calculate relative coordinates within ecDNA
    intersection_df['gene_start_in_ecdna'] = intersection_df['gene_start'] - intersection_df['ecdna_start']
    intersection_df['gene_end_in_ecdna'] = intersection_df['gene_end'] - intersection_df['ecdna_start']
    
    # Get ecDNA size information from original results
    ecdna_size_info = ecdna_df.groupby(['sample_name', 'circ_id']).agg({
        'size_bp': 'first'
    }).reset_index()
    
    # Merge with size information
    result_df = pd.merge(
        intersection_df,
        ecdna_size_info,
        on=['sample_name', 'circ_id'],
        how='left'
    )
    
    # Group by patient and circ_id to get all genes per ecDNA
    grouped_results = []
    
    for (patient, circ_id), group in result_df.groupby(['patient', 'circ_id']):
        # Get basic ecDNA information
        sample_name = group['sample_name'].iloc[0]
        size_bp = group['size_bp'].iloc[0]
        
        # Collect all genes and their coordinates
        genes_info = []
        for _, row in group.iterrows():
            gene_info = {
                'gene_name': row['gene_name'],
                'gene_chr': row['gene_chr'],
                'gene_start': int(row['gene_start']),
                'gene_end': int(row['gene_end']),
                'gene_start_in_ecdna': int(row['gene_start_in_ecdna']),
                'gene_end_in_ecdna': int(row['gene_end_in_ecdna'])
            }
            genes_info.append(gene_info)
        
        # Create comma-separated lists
        gene_names = ','.join([g['gene_name'] for g in genes_info])
        gene_coords = ','.join([f"{g['gene_name']}:{g['gene_start']}-{g['gene_end']}" for g in genes_info])
        gene_coords_in_ecdna = ','.join([f"{g['gene_name']}:{g['gene_start_in_ecdna']}-{g['gene_end_in_ecdna']}" for g in genes_info])
        
        grouped_results.append({
            'patient': patient,
            'circ_id': circ_id,
            'sample_name': sample_name,
            'size_bp': size_bp,
            'n_genes': len(genes_info),
            'gene_names': gene_names,
            'gene_coordinates': gene_coords,
            'gene_coordinates_in_ecdna': gene_coords_in_ecdna
        })
    
    return pd.DataFrame(grouped_results)

def main():
    """Main function"""
    args = parse_arguments()
    
    print("=== Processing ecDNA-Gene Intersections ===")
    print(f"Intersection file: {args.intersected_bed}")
    print(f"ecDNA results file: {args.ecdna_results}")
    print(f"Output file: {args.output_file}")
    
    # Load input files
    intersection_df = load_intersection_results(args.intersected_bed)
    ecdna_df = load_ecdna_results(args.ecdna_results)
    
    if intersection_df.empty:
        print("No intersections found. Creating empty output file.")
        empty_df = pd.DataFrame(columns=[
            'patient', 'circ_id', 'sample_name', 'size_bp', 'n_genes',
            'gene_names', 'gene_coordinates', 'gene_coordinates_in_ecdna'
        ])
        empty_df.to_csv(args.output_file, sep='\t', index=False)
        return
    
    # Process intersections
    result_df = process_intersections(intersection_df, ecdna_df)
    
    if result_df.empty:
        print("No valid intersections processed. Creating empty output file.")
        empty_df = pd.DataFrame(columns=[
            'patient', 'circ_id', 'sample_name', 'size_bp', 'n_genes',
            'gene_names', 'gene_coordinates', 'gene_coordinates_in_ecdna'
        ])
        empty_df.to_csv(args.output_file, sep='\t', index=False)
        return
    
    # Sort results
    result_df = result_df.sort_values(['patient', 'circ_id'])
    
    # Create output directory if needed
    os.makedirs(os.path.dirname(args.output_file), exist_ok=True)
    
    # Save results
    result_df.to_csv(args.output_file, sep='\t', index=False)
    
    print(f"\n=== RESULTS ===")
    print(f"Total ecDNAs with genes: {len(result_df)}")
    print(f"Total unique genes: {len(set([gene for genes in result_df['gene_names'] for gene in genes.split(',')]))}")
    print(f"Results saved to: {args.output_file}")
    
    # Print summary statistics
    if len(result_df) > 0:
        print(f"\nGenes per ecDNA statistics:")
        print(f"  Mean: {result_df['n_genes'].mean():.1f}")
        print(f"  Median: {result_df['n_genes'].median():.1f}")
        print(f"  Max: {result_df['n_genes'].max()}")

if __name__ == "__main__":
    main()
