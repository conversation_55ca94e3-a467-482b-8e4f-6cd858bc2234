# ecDNA reconstruction from Nanopore long-read data
# Author: <PERSON>

###################################
# Preparation for ecDNA reconstruction from long-read data
###################################

# create a dictionary in a file containing the regions to be processed
rule create_region_dict:
    input:
        expand("{res_dir}/analysis_sarek/consensus_cnas/{patient}/002-{patient}-{tumor}_combine_intersected_results_gain.tsv", res_dir = RES_DIR, patient = sample_codes_list, tumor = tumor_types),

        script = BIN_DIR + "/nanopore_episomes/001a-create_region_dict.py"

    output:
        output_dict = RES_DIR + "/analysis_nanopore/ecdna/region_dict.tsv"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '10G'
        
    shell:
        """
        {PYTH<PERSON>} {input.script} --res_dir {RES_DIR} --output_dict {output.output_dict}
        """


# Extract reads overlapping gain regions
rule extract_reads_from_gain_regions:
    input:
        cram_file = RES_DIR + "/nanopore_somatic/{patient}-{tumor}/{patient}-{tumor}_tumor.ht.cram",

        fasta = RES_DIR + "/Homo_sapiens_assembly38.fasta",

        script = BIN_DIR + "/nanopore_episomes/001b-extract_reads_from_gain_regions.py"
        
    output:
        region_bam = RES_DIR + "/analysis_nanopore/ecdna/manual_assembly/{patient}-{tumor}/{region}.bam"
        
    params:
        region = "{region}"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '20G'
        
    shell:
        """
        {PYTHON} {input.script} --cram_file {input.cram_file} --region {params.region} --fasta {input.fasta} --output_bam {output.region_bam} --threads {resources.threads}
        """

# Filter reads by length
rule filter_reads_by_length:
    input:
        region_bam = RES_DIR + "/analysis_nanopore/ecdna/manual_assembly/{patient}-{tumor}/{region}.bam",
        script = BIN_DIR + "/nanopore_episomes/001c-filter_reads_by_length.py"
        
    output:
        filtered_fastq = RES_DIR + "/analysis_nanopore/ecdna/manual_assembly/{patient}-{tumor}/{region}.filtered.fastq"
        
    params:
        region = "{region}",
        length_margin = 0.2  # 20% margin
        
    resources:
        threads = 4,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '8G'
        
    shell:
        """
        {PYTHON} {input.script} --region_bam {input.region_bam} --region {params.region} --length_margin {params.length_margin} --output_fastq {output.filtered_fastq}
        """

# Assemble with Flye
rule assemble_with_flye:
    input:
        filtered_fastq = RES_DIR + "/analysis_nanopore/ecdna/manual_assembly/{patient}-{tumor}/{region}.filtered.fastq",
        script = BIN_DIR + "/nanopore_episomes/001e-assemble_with_flye.py"
        
    output:
        flye_assembly = RES_DIR + "/analysis_nanopore/ecdna/manual_assembly/{patient}-{tumor}/flye/{region}.flye.fasta"
        
    params:
        outdir = RES_DIR + "/analysis_nanopore/ecdna/manual_assembly/{patient}-{tumor}/flye/{region}_flye",
        min_reads = 9

    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore"

    resources:
        threads = 8,
        queue = "all.q",
        jobtime = '3:0:0:0',
        individual_core_memory = '8G'
        
    shell:
        """
        {PYTHON} {input.script} --filtered_fastq {input.filtered_fastq} --output_fasta {output.flye_assembly} --outdir {params.outdir} --min_reads {params.min_reads} --threads {resources.threads}
        """

# Check read count and align reads to themselves with minimap2
rule check_read_count_and_align:
    input:
        filtered_fastq = RES_DIR + "/analysis_nanopore/ecdna/manual_assembly/{patient}-{tumor}/{region}.filtered.fastq",
        script = BIN_DIR + "/nanopore_episomes/001d-check_read_count_and_align.py"
        
    output:
        paf_file = RES_DIR + "/analysis_nanopore/ecdna/manual_assembly/{patient}-{tumor}/miniasm/{region}.paf"
        
    params:
        min_reads = 10
        
    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/wgs_nanopore"
        
    resources:
        threads = 8,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '8G'
        
    shell:
        """
        {PYTHON} {input.script} --input_fastq {input.filtered_fastq} --output_paf {output.paf_file} --min_reads {params.min_reads} --threads {resources.threads}
        """

# Assemble with miniasm
rule miniasm_assembly:
    input:
        filtered_fastq = RES_DIR + "/analysis_nanopore/ecdna/manual_assembly/{patient}-{tumor}/{region}.filtered.fastq",
        paf_file = RES_DIR + "/analysis_nanopore/ecdna/manual_assembly/{patient}-{tumor}/miniasm/{region}.paf",
        script = BIN_DIR + "/nanopore_episomes/001e-assemble_with_miniasm.py"
        
    output:
        miniasm_assembly = RES_DIR + "/analysis_nanopore/ecdna/manual_assembly/{patient}-{tumor}/miniasm/{region}.miniasm.gfa"
        
    params:
        min_reads = 10
        
    resources:
        threads = 8,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '8G'
        
    shell:
        """
        {PYTHON} {input.script} --filtered_fastq {input.filtered_fastq} --paf_file {input.paf_file} --output_gfa {output.miniasm_assembly} --min_reads {params.min_reads} --threads {resources.threads}
        """

# Convert GFA to FASTA
rule gfa_to_fasta:
    input:
        miniasm_assembly = RES_DIR + "/analysis_nanopore/ecdna/manual_assembly/{patient}-{tumor}/miniasm/{region}.miniasm.gfa",
        script = BIN_DIR + "/nanopore_episomes/001f-gfa_to_fasta.py"
        
    output:
        fasta_assembly = RES_DIR + "/analysis_nanopore/ecdna/manual_assembly/{patient}-{tumor}/miniasm/{region}.miniasm.fasta"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '0:1:0:0',
        individual_core_memory = '4G'
        
    shell:
        """
        {PYTHON} {input.script} --input_gfa {input.miniasm_assembly} --output_fasta {output.fasta_assembly}
        """

# Generate summary report for a specific patient-tumor pair
rule generate_ecdna_summary:
    input:
        flye_assemblies = lambda wildcards: [RES_DIR + f"/analysis_nanopore/ecdna/manual_assembly/{wildcards.patient}-{wildcards.tumor}/flye/{region}.flye.fasta" for region in ecdna_sample_dict.get(f"{wildcards.patient}-{wildcards.tumor}", [])],
        miniasm_assemblies = lambda wildcards: [RES_DIR + f"/analysis_nanopore/ecdna/manual_assembly/{wildcards.patient}-{wildcards.tumor}/miniasm/{region}.miniasm.fasta" for region in ecdna_sample_dict.get(f"{wildcards.patient}-{wildcards.tumor}", [])],
        script = BIN_DIR + "/nanopore_episomes/001g-generate_ecdna_summary.py"
        
    output:
        summary_report = RES_DIR + "/analysis_nanopore/ecdna/manual_assembly/{patient}-{tumor}/assembly_summary.tsv"
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '0:1:0:0',
        individual_core_memory = '4G'
        
    shell:
        """
        {PYTHON} {input.script} --flye_assemblies "{input.flye_assemblies}" --miniasm_assemblies "{input.miniasm_assemblies}" --output_report {output.summary_report}
        """

# # Process all regions for a specific patient-tumor pair
# rule process_all_regions:
#     input:
#         flye_assemblies = lambda wildcards: [RES_DIR + f"/analysis_nanopore/ecdna/manual_assembly/{wildcards.patient}-{wildcards.tumor}/{region}.flye.fasta" for region in ecdna_sample_dict.get(f"{wildcards.patient}-{wildcards.tumor}", [])],
#         miniasm_assemblies = lambda wildcards: [RES_DIR + f"/analysis_nanopore/ecdna/manual_assembly/{wildcards.patient}-{wildcards.tumor}/{region}.miniasm.fasta" for region in ecdna_sample_dict.get(f"{wildcards.patient}-{wildcards.tumor}", [])],
#         summary_report = RES_DIR + "/analysis_nanopore/ecdna/manual_assembly/{patient}-{tumor}/assembly_summary.tsv"
    
#     output:
#         flag = RES_DIR + "/analysis_nanopore/ecdna/manual_assembly/{patient}-{tumor}/completed.flag"
    
#     shell:
#         """
#         touch {output.flag}
#         """

# # Main rule to run the entire pipeline for all samples
# rule all_ecdna_reconstruction:
#     input:
#         expand(RES_DIR + "/analysis_nanopore/ecdna/manual_assembly/{sample}/completed.flag", 
#                sample=ecdna_sample_dict.keys())
