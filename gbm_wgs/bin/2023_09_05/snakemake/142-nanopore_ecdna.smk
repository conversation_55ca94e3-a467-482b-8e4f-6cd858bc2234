# Alec Bahcheli
# Nanopore ecDNA analysis pipeline

###################################
# Preparation for Decoil: long-read ecDNA identification
###################################

rule convert_cram_to_bam_for_ecdna:
    input:
        cram_file = RES_DIR + "/nanopore_somatic/{patient}-{tumor}/{patient}-{tumor}_tumor.ht.cram",
        fasta = RES_DIR + "/Homo_sapiens_assembly38.fasta"

    output:
        bam_file = temp(RES_DIR + "/analysis_nanopore/ecdna/bam_files/{patient}-{tumor}.bam")

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '50G'

    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/software/samtools-1.16.1/samtools view -b -T {input.fasta} -o {output.bam_file} {input.cram_file}"""

rule index_bam_file:
    input:
        bam_file = RES_DIR + "/analysis_nanopore/ecdna/bam_files/{patient}-{tumor}.bam"

    output:
        bam_index_file = temp(RES_DIR + "/analysis_nanopore/ecdna/bam_files/{patient}-{tumor}.bam.bai")

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '50G'

    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/software/samtools-1.16.1/samtools index {input.bam_file}"""

# Prepare CoRAL input file
rule prepare_coral_input:
    input:
        cna_merged_file = RES_DIR + "/analysis_sarek/consensus_cnas/{patient}/002-{patient}-{tumor}_combine_intersected_results_gain.tsv",
        cnvkit_cns_file = RES_DIR + "/sarek_wgs/{patient}-{tumor}/variant_calling/cnvkit/{patient}-{tumor}-{tumor}_vs_{patient}-{tumor}-blood/{patient}-{tumor}-{tumor}.call.cns",

        script = BIN_DIR + "/nanopore_episomes/010-coral_gain_regions_preparation.py"

    output:
        coral_input_bed_file = RES_DIR + "/analysis_nanopore/ecdna/coral/{patient}-{tumor}/{patient}-{tumor}_coral_input.bed"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '20G'

    shell:
        """{PYTHON} {input.script} --cna_merged_file {input.cna_merged_file} --cnvkit_cns_file {input.cnvkit_cns_file} --coral_input_bed_file {output.coral_input_bed_file}"""


###################################
# Decoil pipeline
###################################

# rule run_decoil:
#     input:
#         bam_file = RES_DIR + "/analysis_nanopore/ecdna/bam_files/{patient}-{tumor}.bam",
#         bam_index = RES_DIR + "/analysis_nanopore/ecdna/bam_files/{patient}-{tumor}.bam.bai",
#         fasta = RES_DIR + "/Homo_sapiens_assembly38.fasta",
#         gtf = REF_DATA_DIR + "/GCF_000001405.40_GRCh38.p14_genomic.gtf"

#     output:
#         RES_DIR + "/analysis_nanopore/ecdna/decoil/{patient}-{tumor}/summary.txt",
#         RES_DIR + "/analysis_nanopore/ecdna/decoil/{patient}-{tumor}/reconstruct.ecDNA.filtered.fasta"

#     params:
#         name = "{patient}-{tumor}",
#         logs_dir = RES_DIR + "/analysis_nanopore/ecdna/decoil/{patient}-{tumor}/logs",
#         tmp_dir = RES_DIR + "/analysis_nanopore/ecdna/decoil/{patient}-{tumor}/tmp",
#         decoil_sif = "/.mounts/labs/reimandlab/private/users/abahcheli/software/decoil/decoil.sif",
#         output_dir = directory(RES_DIR + "/analysis_nanopore/ecdna/decoil/{patient}-{tumor}/")

#     conda:
#         "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/nextflow"

#     resources:
#         threads = 5,
#         queue = "all.q",
#         jobtime = '10:0:0:0',
#         individual_core_memory = '20G'

#     shell:
#         """
#         mkdir -p {params.output_dir}
#         mkdir -p {params.logs_dir}
#         mkdir -p {params.tmp_dir}
        
#         singularity run \
#             --bind {params.logs_dir}:/mnt/logs \
#             --bind {params.tmp_dir}:/tmp \
#             --bind {input.bam_file}:/data/input.bam \
#             --bind {input.bam_index}:/data/input.bam.bai \
#             --bind {input.fasta}:/annotation/reference.fa \
#             --bind {input.gtf}:/annotation/anno.gtf \
#             --bind {params.output_dir}:/mnt \
#             {params.decoil_sif} \
#             decoil-pipeline sv-reconstruct \
#                 -b /data/input.bam \
#                 -r /annotation/reference.fa \
#                 -g /annotation/anno.gtf \
#                 -o /mnt --name {params.name}
#         """

###################################
# Summarize and visualize Decoil results
###################################

# combine decoil results from all samples
rule combine_decoil_results:
    input:
        summary_files = expand(RES_DIR + "/analysis_nanopore/ecdna/decoil/{sample}/summary.txt", sample=sample_list),
        bed_files = expand(RES_DIR + "/analysis_nanopore/ecdna/decoil/{sample}/reconstruct.ecDNA.filtered.bed", sample=sample_list),

        script = BIN_DIR + "/circ_dna_scripts/002a-combine_decoil_results.py"

    output:
        combined_results = RES_DIR + "/analysis_nanopore/ecdna/decoil/combined_decoil_results.tsv",
        summary_results = RES_DIR + "/analysis_nanopore/ecdna/decoil/combined_decoil_results_summary.tsv",
        ecdna_counts = RES_DIR + "/analysis_nanopore/ecdna/decoil/ecdna_counts.tsv"


    params:
        sample_list = ",".join(sample_list)

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '10G'

    shell:
        "{PYTHON} {input.script} --results_dir {RES_DIR} --sample_list {params.sample_list} --combined_results {output.combined_results} --summary_results {output.summary_results}"


# visualize decoil results
rule visualize_decoil_results:
    input:
        summary_results = RES_DIR + "/analysis_nanopore/ecdna/decoil/combined_decoil_results_summary.tsv",
        ecdna_counts = RES_DIR + "/analysis_nanopore/ecdna/decoil/ecdna_counts.tsv",

        script = BIN_DIR + "/circ_dna_scripts/002b-visualize_decoil_results.R"

    output:
        summary_plot = RES_DIR + "/analysis_nanopore/ecdna/_figures/decoil_summary.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '15G'

    shell:
        "{RSCRIPT} {input.script} --summary_results {input.summary_results} --ecdna_counts {input.ecdna_counts}  --summary_plot {output.summary_plot}"


###################################
# Intersect ecDNA regions with genes
###################################

# intersect individual sample ecDNA regions with protein-coding genes
rule intersect_ecdna_genes:
    input:
        ecdna_bed = RES_DIR + "/analysis_nanopore/ecdna/decoil/{sample}/reconstruct.ecDNA.filtered.bed",
        genes_bed = RES_DIR + "/analysis_sarek/gene_impacts/all_combined_pcg_cnas.bed",
        fasta_file = RES_DIR + "/analysis_nanopore/ecdna/decoil/{sample}/reconstruct.ecDNA.filtered.fasta",
        script = BIN_DIR + "/circ_dna_scripts/003a-intersect_ecdna_genes.py"

    output:
        intersected_genes = RES_DIR + "/analysis_nanopore/ecdna/decoil/{sample}/{sample}_ecdna_genes.tsv"

    params:
        sample_name = "{sample}"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '10G'

    shell:
        """
        {PYTHON} {input.script} \
            --ecdna_bed_file {input.ecdna_bed} \
            --genes_bed_file {input.genes_bed} \
            --sample_name {params.sample_name} \
            --output_file {output.intersected_genes} \
            --fasta_file {input.fasta_file}
        """


# combine all ecDNA-gene intersections
rule combine_ecdna_gene_intersections:
    input:
        intersected_files = expand(RES_DIR + "/analysis_nanopore/ecdna/decoil/{sample}/{sample}_ecdna_genes.tsv", sample=sample_list),
        script = BIN_DIR + "/circ_dna_scripts/003b-combine_ecdna_gene_intersections.py"

    output:
        combined_intersections = RES_DIR + "/analysis_nanopore/ecdna/decoil/combined_ecdna_gene_intersections.tsv",
        summary_stats = RES_DIR + "/analysis_nanopore/ecdna/decoil/combined_ecdna_gene_intersections_summary_stats.tsv"

    params:
        input_files = lambda wildcards, input: ",".join(input.intersected_files)

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '10G'

    shell:
        """
        {PYTHON} {input.script} \
            --input_files {params.input_files} \
            --output_file {output.combined_intersections}
        """




