# Alec Ba<PERSON>cheli
# Nanopore ecDNA analysis pipeline

###################################
# Preparation for CoRAL: long-read ecDNA identification
###################################

# rule ecdna_nanopore_dirs:
#     input:
#         null = RES_DIR + "/analysis_nanopore/null.txt"

#     output:
#         cnvkit_null_txt = RES_DIR + "/analysis_nanopore/ecdna/cnvkit/{sample}/null.txt"

#     params:
#         cnvkit_dir = RES_DIR + "/analysis_nanopore/ecdna/cnvkit/{sample}"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '1:0:0:0',
#         individual_core_memory = '5G'
        
#     shell:
#         """
#         mkdir -p {params.cnvkit_dir}
#         touch {output.cnvkit_null_txt}
#         """

# # Map nanopore reads and output BAM directly
# rule minimap2:
#     input:
#         ubam_file = RES_DIR + "/nanopore/{patient}-{tumor}/{patient}-{tumor}.pass.bam",
#         fasta = RES_DIR + "/Homo_sapiens_assembly38.fasta",
#         fasta_index = RES_DIR + "/Homo_sapiens_assembly38.fasta.fai"

#     output:
#         bam_file = RES_DIR + "/ecdna/bam_files/{patient}-{tumor}.bam"

#     params:
#         sorting_prefix = RES_DIR + "/ecdna/bam_files/sorting",

#     resources:
#         threads = 20,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '4G'

#     shell:
#         """
#         mkdir -p {params.output_dir}
#         /.mounts/labs/reimandlab/private/users/abahcheli/software/samtools-1.16.1/samtools fastq {input.ubam_file} | \
#         /.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/minimap2/bin/minimap2 -ax map-ont {input.fasta} -t {resources.threads} - | \
#         /.mounts/labs/reimandlab/private/users/abahcheli/software/samtools-1.16.1/samtools sort -T {params.sorting_prefix} -@ {resources.threads} -o {output.bam_file}
#         """

rule convert_cram_to_bam_for_ecdna:
    input:
        cram_file = RES_DIR + "/nanopore_somatic/{patient}-{tumor}/{patient}-{tumor}_tumor.ht.cram",
        fasta = RES_DIR + "/Homo_sapiens_assembly38.fasta"

    output:
        bam_file = RES_DIR + "/analysis_nanopore/ecdna/bam_files/{patient}-{tumor}.bam"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '50G'

    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/software/samtools-1.16.1/samtools view -b -T {input.fasta} -o {output.bam_file} {input.cram_file}"""

rule index_bam_file:
    input:
        bam_file = RES_DIR + "/ecdna/bam_files/{patient}-{tumor}.bam"

    output:
        bam_index_file = RES_DIR + "/ecdna/bam_files/{patient}-{tumor}.bam.bai"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '50G'

    shell:
        """/.mounts/labs/reimandlab/private/users/abahcheli/software/samtools-1.16.1/samtools index {input.bam_file}"""

# Prepare CoRAL input file
rule prepare_coral_input:
    input:
        cna_merged_file = RES_DIR + "/analysis_sarek/consensus_cnas/{patient}/002-{patient}-{tumor}_combine_intersected_results_gain.tsv",
        cnvkit_cns_file = RES_DIR + "/sarek_wgs/{patient}-{tumor}/variant_calling/cnvkit/{patient}-{tumor}-{tumor}_vs_{patient}-{tumor}-blood/{patient}-{tumor}-{tumor}.call.cns",

        script = BIN_DIR + "/nanopore_episomes/010-coral_gain_regions_preparation.py"

    output:
        coral_input_bed_file = RES_DIR + "/analysis_nanopore/ecdna/coral/{patient}-{tumor}/{patient}-{tumor}_coral_input.bed"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '20G'

    shell:
        """{PYTHON} {input.script} --cna_merged_file {input.cna_merged_file} --cnvkit_cns_file {input.cnvkit_cns_file} --coral_input_bed_file {output.coral_input_bed_file}"""


###################################
# Decoil pipeline
###################################

rule run_decoil:
    input:
        bam_file = RES_DIR + "/analysis_nanopore/ecdna/bam_files/{patient}-{tumor}.bam",
        bam_index = RES_DIR + "/analysis_nanopore/ecdna/bam_files/{patient}-{tumor}.bam.bai",
        fasta = RES_DIR + "/Homo_sapiens_assembly38.fasta",
        gtf = REF_DATA_DIR + "/GCF_000001405.40_GRCh38.p14_genomic.gtf"

    output:
        results = RES_DIR + "/analysis_nanopore/ecdna/decoil/{patient}_{tumor}/reconstruct.fasta"
        
    params:
        name = "{patient}_{tumor}",
        logs_dir = RES_DIR + "/analysis_nanopore/ecdna/decoil/{patient}_{tumor}/logs",
        tmp_dir = RES_DIR + "/analysis_nanopore/ecdna/decoil/{patient}_{tumor}/tmp",
        decoil_sif = "/.mounts/labs/reimandlab/private/users/abahcheli/software/decoil/decoil.sif",
        output_dir = directory(RES_DIR + "/analysis_nanopore/ecdna/decoil/{patient}_{tumor}/")

    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/nextflow"

    resources:
        threads = 8,
        queue = "all.q",
        jobtime = '10:0:0:0',
        individual_core_memory = '10G'

    shell:
        """
        mkdir -p {params.output_dir}
        mkdir -p {params.logs_dir}
        mkdir -p {params.tmp_dir}
        
        singularity run \
            --bind {params.logs_dir}:/mnt/logs \
            --bind {params.tmp_dir}:/tmp \
            --bind {input.bam_file}:/data/input.bam \
            --bind {input.bam_index}:/data/input.bam.bai \
            --bind {input.fasta}:/annotation/reference.fa \
            --bind {input.gtf}:/annotation/anno.gtf \
            --bind {params.output_dir}:/mnt \
            {params.decoil_sif} \
            decoil-pipeline sv-reconstruct \
                -b /data/input.bam \
                -r /annotation/reference.fa \
                -g /annotation/anno.gtf \
                -o /mnt --name {params.name}
        """


###################################
# Running CoRAL
###################################

# Run CoRAL seed
rule coral_seed:
    input:
        bam_file = RES_DIR + "/analysis_nanopore/ecdna/bam_files/{patient}-{tumor}.bam",
        bed_file = RES_DIR + "/analysis_nanopore/ecdna/coral/{patient}-{tumor}/{patient}-{tumor}_coral_input.bed"

    output:
        cns_seed_file = RES_DIR + "/analysis_nanopore/ecdna/coral/{patient}-{tumor}/{patient}-{tumor}_CNV_SEEDS.bed"

    params:
        coral_prefix = RES_DIR + "/analysis_nanopore/ecdna/coral/{patient}-{tumor}/{patient}-{tumor}"

    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/coral"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '30G'

    shell:
        """coral seed --cn-seg {input.bed_file} --output-prefix {params.coral_prefix} --gain 6.0 --min-seed-size 100000 --max-seg-gap 300000"""

# Run CoRAL reconstruct
# The --min-bp-support parameter is set to 10 to ensure that only segments with at least 10 bp of support are included in the graph.
rule coral_reconstruct:
    input:
        bam_file = RES_DIR + "/analysis_nanopore/ecdna/bam_files/{patient}-{tumor}.bam",
        bam_file_index = RES_DIR + "/analysis_nanopore/ecdna/bam_files/{patient}-{tumor}.bam.bai",

        bed_file = RES_DIR + "/analysis_nanopore/ecdna/coral/{patient}-{tumor}/{patient}-{tumor}_coral_input.bed",
        cns_seed_file = RES_DIR + "/analysis_nanopore/ecdna/coral/{patient}-{tumor}/{patient}-{tumor}_CNV_SEEDS.bed"

    output:
        cns_reconstructed_ecDNAs_file = RES_DIR + "/analysis_nanopore/ecdna/coral/{patient}-{tumor}/{patient}-{tumor}_graph.txt"

    params:
        prefix = RES_DIR + "/analysis_nanopore/ecdna/coral/{patient}-{tumor}/{patient}-{tumor}",
        min_bp_support = 10

    conda:
        "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/coral"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '50G'

    shell:
        """coral reconstruct --lr-bam {input.bam_file} --cnv-seed {input.cns_seed_file} --cn-seg {input.bed_file} --min-bp-support {params.min_bp_support} --output-prefix {params.prefix} --solver scip"""


# rule coral_cycle:
#     input:
#         graph_file = RES_DIR + "/analysis_nanopore/ecdna/coral/{patient}-{tumor}_graph.txt"

#     output:
#         cycles_file = RES_DIR + "/analysis_nanopore/ecdna/coral/{patient}-{tumor}_cycles.txt"

#     conda:
#         "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/coral_py312"

#     conda:
#         "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/coral_py312"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '50G'

#     shell:
#         """coral cycle --graph {input.graph_file} --output-prefix {output.cycles_file}"""


# rule coral_plot:
#     input:
#         cycles_file = RES_DIR + "/analysis_nanopore/ecdna/coral/{patient}-{tumor}_cycles.txt",
#         cns_file = RES_DIR + "/analysis_nanopore/ecdna/cnvkit/{patient}-{tumor}/{patient}-{tumor}_coral_input.cns"

#     output:
#         plot_dir = directory(RES_DIR + "/analysis_nanopore/ecdna/coral/{patient}-{tumor}_plots")

#     params:
#         plot_prefix = RES_DIR + "/analysis_nanopore/ecdna/coral/{patient}-{tumor}_plots/{patient}-{tumor}"

#     conda:
#         "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/coral_py312"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '50G'

#     shell:
#         """
#         mkdir -p {output.plot_dir}
#         coral plot --cycles {input.cycles_file} --cn-seg {input.cns_file} --output-prefix {params.plot_prefix}
#         """


# rule coral_hsr:
#     input:
#         cycles_file = RES_DIR + "/analysis_nanopore/ecdna/coral/{patient}-{tumor}_cycles.txt",
#         cns_file = RES_DIR + "/analysis_nanopore/ecdna/cnvkit/{patient}-{tumor}/{patient}-{tumor}_coral_input.cns"

#     output:
#         hsr_file = RES_DIR + "/analysis_nanopore/ecdna/coral/{patient}-{tumor}_hsr.txt"

#     conda:
#         "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/coral_py312"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '50G'

#     shell:
#         """coral hsr --cycles {input.cycles_file} --cn-seg {input.cns_file} --output-prefix {output.hsr_file}"""


# rule coral_cycle2bed:
#     input:
#         cycles_file = RES_DIR + "/analysis_nanopore/ecdna/coral/{patient}-{tumor}_cycles.txt"

#     output:
#         bed_file = RES_DIR + "/analysis_nanopore/ecdna/coral/{patient}-{tumor}_cycles.bed"

#     conda:
#         "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/coral_py312"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '50G'

#     shell:
#         """coral cycle2bed --cycles {input.cycles_file} --output-prefix {output.bed_file}"""


# # Add a summary rule to generate a report of all CoRAL results
# rule coral_summary:
#     input:
#         graph_files = expand(RES_DIR + "/analysis_nanopore/ecdna/coral/{patient}-{tumor}_graph.txt", 
#                             patient=PATIENTS, tumor=TUMORS),
#         cycles_files = expand(RES_DIR + "/analysis_nanopore/ecdna/coral/{patient}-{tumor}_cycles.txt", 
#                              patient=PATIENTS, tumor=TUMORS),
#         hsr_files = expand(RES_DIR + "/analysis_nanopore/ecdna/coral/{patient}-{tumor}_hsr.txt", 
#                           patient=PATIENTS, tumor=TUMORS),
#         bed_files = expand(RES_DIR + "/analysis_nanopore/ecdna/coral/{patient}-{tumor}_cycles.bed", 
#                           patient=PATIENTS, tumor=TUMORS),
#         script = BIN_DIR + "/2023_09_05/nanopore_episomes/coral_summary.py"

#     output:
#         summary_file = RES_DIR + "/analysis_nanopore/ecdna/coral/summary.tsv",
#         summary_plot = RES_DIR + "/analysis_nanopore/ecdna/coral/summary_plot.pdf"

#     conda:
#         "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/coral_py312"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '50G'

#     shell:
#         """
#         {PYTHON} {input.script} \
#         --graph_files "{input.graph_files}" \
#         --annotation_files "{input.cycles_files}" \
#         --summary_file {output.summary_file} \
#         --summary_plot {output.summary_plot}
#         """


# # Main rule to run the entire CoRAL pipeline
# rule all_coral_ecdna:
#     input:
#         RES_DIR + "/analysis_nanopore/ecdna/coral/summary.tsv",
#         RES_DIR + "/analysis_nanopore/ecdna/coral/summary_plot.pdf",
#         expand(RES_DIR + "/analysis_nanopore/ecdna/coral/{patient}-{tumor}_plots", 
#                patient=PATIENTS, tumor=TUMORS)


# ###################################
# # Running decoil
# ###################################

# # Calculate coverage
# rule bigwig_coverage_from_bam:
#     input:
#         bam_file = RES_DIR + "/analysis_nanopore/ecdna/bam_files/{patient}-{tumor}.bam",
#         bam_file_index = RES_DIR + "/analysis_nanopore/ecdna/bam_files/{patient}-{tumor}.bam.bai"

#     output:
#         bigwig_coverage_file = RES_DIR + "/analysis_nanopore/ecdna/coverage/{patient}-{tumor}.bw"

#     conda:
#         "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/deeptools"

#     resources:
#         threads = 20,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '5G'

#     shell:
#         """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/deeptools/bin/bamCoverage -b {input.bam_file} -o {output.bigwig_coverage_file} --binSize 50 --numberOfProcessors {resources.threads}"""

# # Main rule to run the entire pipeline
# rule all_nanopore_ecdna:
#     input:
#         expand(RES_DIR + "/analysis_nanopore/ecdna/coverage/{patient}-{tumor}.bw", 
#                patient=PATIENTS, tumor=TUMORS)

# # reconstruct ecDNAs
# rule decoil_reconstruct:
#     input:
#         bam_file = RES_DIR + "/analysis_nanopore/ecdna/bam_files/{patient}-{tumor}.bam",
#         bam_file_index = RES_DIR + "/analysis_nanopore/ecdna/bam_files/{patient}-{tumor}.bam.bai",

#         vcf_file = RES_DIR + "/analysis_nanopore/processing_non_somatic_svs/somatic_svs/{patient}-{tumor}_vs_blood-somatic.vcf",
#         bigwig_coverage_file = RES_DIR + "/analysis_nanopore/ecdna/coverage/{patient}-{tumor}.bw",

#         genome_fasta = RES_DIR + "/Homo_sapiens_assembly38.fasta"

#     output:
#         results_file = RES_DIR + "/analysis_nanopore/ecdna/decoil/{patient}-{tumor}/decoil_results.tsv"

#     params:
#         sample_name = "{patient}-{tumor}",
#         output_dir = RES_DIR + "/analysis_nanopore/ecdna/decoil/{patient}-{tumor}"

#     conda:
#         "/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/envdecoil/bin/decoil"

#     resources:
#         threads = 1,
#         queue = "all.q",
#         jobtime = '7:0:0:0',
#         individual_core_memory = '50G'

#     shell:
#         """/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/envdecoil/bin/decoil reconstruct -b {input.bam_file} -i {input.vcf_file} -c {input.bigwig_coverage_file} --outputdir {params.output_dir} --name {params.sample_name} -r {input.genome_fasta} --sv-caller sniffles2"""


