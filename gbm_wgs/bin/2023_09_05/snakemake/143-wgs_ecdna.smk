# Alec Bahcheli
# uses circdna: https://nf-co.re/circdna/1.1.0/


###################################
# Setup nextflow pipelines - AmpliconArchitect ONLY
###################################
# create directory if it does not already exist
rule setup_wgs_circdna_nextflow:
    input:
        script = BIN_DIR + "/circ_dna/001a-setup_circ_dna.py",
        fasta_file = RES_DIR + "/Homo_sapiens_assembly38.fasta"

    output:
        expand("{bin_dir}/circ_dna/{sample}", bin_dir = BIN_DIR, sample = sample_list),
        expand("{bin_dir}/circ_dna/{sample}.csv", bin_dir = BIN_DIR, sample = sample_list)

    params:
        bin_dir = BIN_DIR + "/circ_dna",
        results_dir = RES_DIR + "/circ_dna",
        mosek_license_dir = "/.mounts/labs/reimandlab/private/users/abahcheli/epi2me-workflows/nextflow_pipelines_offline/nf-core-circdna_1.1.0/mosek_license_dir",
        bwa_mem_hg38_index_dir = "/.mounts/labs/reimandlab/private/users/abahcheli/epi2me-workflows/igenomes/Homo_sapiens/GATK/GRCh38/Sequence/BWAIndex",
        aa_data_repo = RES_DIR + "/circ_dna/data_repo"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '10G'
        
    shell:
        "{PYTHON} {input.script} --fastq_dir {FASTQ_DIR} --bin_dir {params.bin_dir} --fasta_file {input.fasta_file} --mosek_license_dir {params.mosek_license_dir} --aa_data_repo {params.aa_data_repo} --bwa_mem_hg38_index_dir {params.bwa_mem_hg38_index_dir} --results_dir {params.results_dir}"



###################################
# process the results to summarize ecDNAs
###################################

# for testing
spec_samples = ['RLGS1-primary', 'RLGS1-recurrent', 'RLGS2-primary', 'RLGS2-recurrent', 'RLGS3-primary', 'RLGS3-recurrent', 'RLGS4-recurrent', 'RLGS5-primary', 'RLGS5-recurrent', 'RLGS6-primary', 'RLGS6-recurrent', 'RLGS7-primary', 'RLGS9-recurrent',  'RLGS11-primary']

# combine amplicon results from all samples
rule combine_amplicon_results:
    input:
        expand("{res_dir}/circ_dna/{sample}/ampliconsuite/ampliconclassifier/{sample}_feature_basic_properties.tsv", res_dir = RES_DIR, sample = spec_samples),
        expand("{res_dir}/circ_dna/{sample}/ampliconsuite/ampliconclassifier/{sample}_gene_list.tsv", res_dir = RES_DIR, sample = spec_samples),

        script = BIN_DIR + "/circ_dna_scripts/001a-combine_amplicon_results.py",
        # Input files will be dynamically found by the script based on sample list

    output:
        combined_results = RES_DIR + "/circ_dna/combined_amplicon_results.tsv"

    params:
        results_dir = RES_DIR,
        sample_list = ",".join(spec_samples)

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '10G'

    shell:
        "{PYTHON} {input.script} --results_dir {params.results_dir} --sample_list {params.sample_list} --output_file {output.combined_results}"


# visualize amplicon results
rule visualize_amplicon_results:
    input:
        combined_results = RES_DIR + "/circ_dna/combined_amplicon_results.tsv",
        script = BIN_DIR + "/circ_dna_scripts/001b-visualize_amplicon_results.R"

    output:
        figure_file = RES_DIR + "/circ_dna/_figures/amplicon_scatter_plot.pdf"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '15G'

    shell:
        """{RSCRIPT} {input.script} --input_file {input.combined_results} --figure_file {output.figure_file}"""






