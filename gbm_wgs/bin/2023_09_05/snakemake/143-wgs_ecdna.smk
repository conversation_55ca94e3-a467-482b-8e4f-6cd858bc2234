# Alec Bahcheli
# uses circdna: https://nf-co.re/circdna/1.1.0/


###################################
# Setup nextflow pipelines - AmpliconArchitect ONLY
###################################
# create directory if it does not already exist
rule setup_wgs_circdna_nextflow:
    input:
        script = BIN_DIR + "/circ_dna/001a-setup_circ_dna.py",
        fasta_file = RES_DIR + "/Homo_sapiens_assembly38.fasta"

    output:
        expand("{bin_dir}/circ_dna/{sample}", bin_dir = BIN_DIR, sample = sample_list),
        expand("{bin_dir}/circ_dna/{sample}.csv", bin_dir = BIN_DIR, sample = sample_list)

    params:
        bin_dir = BIN_DIR + "/circ_dna",
        results_dir = RES_DIR + "/circ_dna",
        mosek_license_dir = "/.mounts/labs/reimandlab/private/users/abahcheli/epi2me-workflows/nextflow_pipelines_offline/nf-core-circdna_1.1.0/mosek_license_dir",
        bwa_mem_hg38_index_dir = "/.mounts/labs/reimandlab/private/users/abahcheli/epi2me-workflows/igenomes/Homo_sapiens/GATK/GRCh38/Sequence/BWAIndex",
        aa_data_repo = RES_DIR + "/circ_dna/data_repo"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '7:0:0:0',
        individual_core_memory = '10G'
        
    shell:
        "{PYTHON} {input.script} --fastq_dir {FASTQ_DIR} --bin_dir {params.bin_dir} --fasta_file {input.fasta_file} --mosek_license_dir {params.mosek_license_dir} --aa_data_repo {params.aa_data_repo} --bwa_mem_hg38_index_dir {params.bwa_mem_hg38_index_dir} --results_dir {params.results_dir}"



###################################
# process the results to summarize ecDNAs
###################################

# combine amplicon results from all samples
rule combine_amplicon_results:
    input:
        script = BIN_DIR + "/circ_dna_scripts/001a-combine_amplicon_results.py",
        # Input files will be dynamically found by the script based on sample list

    output:
        combined_results = RES_DIR + "/circ_dna/combined_amplicon_results.tsv"

    params:
        results_dir = RES_DIR,
        sample_list = ",".join(sample_list)

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '10G'

    shell:
        "{PYTHON} {input.script} --results_dir {params.results_dir} --sample_list {params.sample_list} --output_file {output.combined_results}"


# visualize amplicon results
rule visualize_amplicon_results:
    input:
        combined_results = RES_DIR + "/circ_dna/combined_amplicon_results.tsv",
        script = BIN_DIR + "/circ_dna_scripts/001b-visualize_amplicon_results.R"

    output:
        summary_plot = RES_DIR + "/circ_dna/_figures/amplicon_summary.pdf",
        summary_stats = RES_DIR + "/circ_dna/_figures/amplicon_summary_stats.tsv"

    params:
        output_prefix = RES_DIR + "/circ_dna/_figures/amplicon"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '15G'

    shell:
        """
        mkdir -p {RES_DIR}/circ_dna/_figures
        {RSCRIPT} {input.script} {input.combined_results} {params.output_prefix}
        """


# test rule using example files
rule test_amplicon_analysis:
    input:
        script = BIN_DIR + "/circ_dna_scripts/001a-combine_amplicon_results.py",
        r_script = BIN_DIR + "/circ_dna_scripts/001b-visualize_amplicon_results.R"

    output:
        test_combined = "/Users/<USER>/Desktop/tmp/test_combined_amplicon_results.tsv",
        test_plot = "/Users/<USER>/Desktop/tmp/test_amplicon_summary.pdf"

    params:
        test_basic_properties = "/Users/<USER>/Desktop/RLGS2-primary_feature_basic_properties.tsv",
        test_gene_list = "/Users/<USER>/Desktop/RLGS2-primary_gene_list.tsv",
        test_output_prefix = "/Users/<USER>/Desktop/tmp/test_amplicon"

    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '1:0:0:0',
        individual_core_memory = '10G'

    shell:
        """
        mkdir -p /Users/<USER>/Desktop/tmp
        {PYTHON} {input.script} --test_mode --test_basic_properties {params.test_basic_properties} --test_gene_list {params.test_gene_list} --results_dir /tmp --sample_list RLGS2-primary --output_file {output.test_combined}
        {RSCRIPT} {input.r_script} {output.test_combined} {params.test_output_prefix}
        """




