# Alec Bahcheli

# configfile: "/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/bin/snakemake_config.yaml"

# run version (typically date)
VERSION='2023_09_05'

# project directory
MAIN_DIR='/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs'

# directory of scripts / binaries
BIN_DIR = "/".join([MAIN_DIR, "bin", VERSION])



# results directories
DATA_DIR = "/".join([MAIN_DIR, "data", VERSION])
REF_DATA_DIR = "/".join([MAIN_DIR, "data", "ref_data"])
RAW_DATA_DIR= "/".join([MAIN_DIR, "data", "raw_data"])

RES_DIR = "/".join([MAIN_DIR, "results", VERSION])
FIGURE_DATA_DIR = "/".join([RES_DIR, "_figure_data"])
FIGURE_DIR = "/".join([RES_DIR, "_figures"])



# location of R environment for running R scripts 
RSCRIPT='/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r4_env/bin/Rscript'
# location of python
PYTHON='/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/py_dev/bin/python'

# R for activepathways and manual packages
AP_RSCRIPT = '/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r_dev/bin/Rscript'
RDEV_SCRIPT = '/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/r_dev/bin/Rscript'
DGEA_RSCRIPT = '/.mounts/labs/reimandlab/private/users/abahcheli/mambaforge/envs/dgea/bin/Rscript'


# fastq directory
## NOTE assumes that reads are paired-end and end with either "...R1.fastq.gz" or "...R2.fastq.gz"
FASTQ_DIR = "/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/data/raw_data/all_fastqs"

# a fasta file of the genome that you are mapping to
## NOTE must be in the WGS_MAIN_DIR
GENOME_VERSION = "Homo_sapiens_assembly38"

# a fasta file of the genome that you are mapping to
genome_fasta = RES_DIR + "/Homo_sapiens_assembly38.fasta"
t2t_genome_fasta = RES_DIR + "/chm13v2.0.fa"

# gtf file for calculating counts and tpm
GTF_FILE = REF_DATA_DIR + "/GCF_000001405.40_GRCh38.p14_genomic.gtf"
# GFF_FILE = REF_DATA_DIR + "/GCF_000001405.40_GRCh38.p14_genomic.protein_coding_genes.gff"
GFF_FILE = REF_DATA_DIR + "/gencode.v45.chr_patch_hapl_scaff.annotation.gff3"



###########################
# WGS sequence processing
###########################

# number of threads to use in each process for mapping
WGS_THREADS = '20'

wgs_sample_list_with_blood = ['RLGS1-primary_wgs_seq', 'RLGS1-recurrent_wgs_seq', 'RLGS1-blood_wgs_seq', 'RLGS2-primary_wgs_seq', 'RLGS2-recurrent_wgs_seq', 'RLGS2-blood_wgs_seq', 'RLGS3-primary_wgs_seq', 'RLGS3-recurrent_wgs_seq', 'RLGS3-blood_wgs_seq', 'RLGS4-primary_wgs_seq', 'RLGS4-recurrent_wgs_seq', 'RLGS4-blood_wgs_seq', 'RLGS5-primary_wgs_seq', 'RLGS5-recurrent_wgs_seq', 'RLGS5-blood_wgs_seq', 'RLGS6-primary_wgs_seq', 'RLGS6-recurrent_wgs_seq', 'RLGS6-blood_wgs_seq', 'RLGS7-primary_wgs_seq', 'RLGS7-recurrent_wgs_seq', 'RLGS7-blood_wgs_seq', 'RLGS8-primary_wgs_seq', 'RLGS8-recurrent_wgs_seq', 'RLGS8-blood_wgs_seq', 'RLGS9-primary_wgs_seq', 'RLGS9-recurrent_wgs_seq', 'RLGS9-blood_wgs_seq', 'RLGS10-primary_wgs_seq', 'RLGS10-recurrent_wgs_seq', 'RLGS10-blood_wgs_seq', 'RLGS11-primary_wgs_seq', 'RLGS11-recurrent_wgs_seq', 'RLGS11-blood_wgs_seq', 'RLGS12-primary_wgs_seq', 'RLGS12-recurrent_wgs_seq', 'RLGS12-blood_wgs_seq']
wgs_sample_list = ['RLGS1-primary_wgs_seq', 'RLGS1-recurrent_wgs_seq', 'RLGS2-primary_wgs_seq', 'RLGS2-recurrent_wgs_seq', 'RLGS3-primary_wgs_seq', 'RLGS3-recurrent_wgs_seq', 'RLGS4-primary_wgs_seq', 'RLGS4-recurrent_wgs_seq', 'RLGS5-primary_wgs_seq', 'RLGS5-recurrent_wgs_seq', 'RLGS6-primary_wgs_seq', 'RLGS6-recurrent_wgs_seq', 'RLGS7-primary_wgs_seq', 'RLGS7-recurrent_wgs_seq', 'RLGS8-primary_wgs_seq', 'RLGS8-recurrent_wgs_seq', 'RLGS9-primary_wgs_seq', 'RLGS9-recurrent_wgs_seq', 'RLGS10-primary_wgs_seq', 'RLGS10-recurrent_wgs_seq', 'RLGS11-primary_wgs_seq', 'RLGS11-recurrent_wgs_seq', 'RLGS12-primary_wgs_seq', 'RLGS12-recurrent_wgs_seq']

tumor_types = ['primary', 'recurrent']




# type of RNA-seq analysis
rna_sample_list = wgs_sample_list.copy()

dgea_analysis_types = ['deseq2_paired', 'edger_paired']
rnaseq_data_types = ['gene_counts', 'transcript_counts']


# nanopore and WGS analysis
samples_csv = "RLGS1-primary,RLGS1-recurrent,RLGS2-primary,RLGS2-recurrent,RLGS3-primary,RLGS3-recurrent,RLGS4-primary,RLGS4-recurrent,RLGS5-primary,RLGS5-recurrent,RLGS6-primary,RLGS6-recurrent,RLGS7-primary,RLGS7-recurrent,RLGS8-primary,RLGS8-recurrent,RLGS9-primary,RLGS9-recurrent,RLGS10-primary,RLGS10-recurrent,RLGS11-primary,RLGS11-recurrent,RLGS12-primary,RLGS12-recurrent"
sample_list = samples_csv.split(",")

sample_codes_list = set([sample.split("-")[0] for sample in sample_list])


# methylation calling might be different
methylation_patient_csv = ",".join(sample_codes_list)



# # cna regions for episome analysis
# cna_bed_file = '/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/results/2023_09_05/analysis_nanopore/episomes/combined_cnas.tsv'
# region_list = [line.strip().replace("\t", "_") for line in open(cna_bed_file).readlines()]

# # extract the reads correlating with the cna regions
# region_to_sample = {region: region.split("_")[-1] for region in region_list}



import os
import numpy as np

# Directory containing the files
fastq_directory = "/.mounts/labs/reimandlab/private/users/abahcheli/reimand_lab_ab/gbm_wgs/data/raw_data/all_fastqs"

# dictionary for associating files with subfiles
marked_duplicates_dict = {}

# List files in the directory
for filename in os.listdir(fastq_directory):
    # Check if the item is a file
    if "wgs" in filename:
        # Split the filename by "_" and get the first 3 parts as the prefix
        prefix = "_".join(filename.split("_")[:3])
        marked_duplicates_file = f"{RES_DIR}/wgs_processing/" + "_".join(filename.split("_")[:4]) + "-marked_duplicates.bam"
        
        # Add the filename to the dictionary under the prefix
        if prefix in marked_duplicates_dict:
            marked_duplicates_dict[prefix].append(marked_duplicates_file)
        else:
            marked_duplicates_dict[prefix] = [marked_duplicates_file]

for prefix in marked_duplicates_dict.keys():
    marked_duplicates_dict[prefix] = list(np.unique(marked_duplicates_dict[prefix]))

def process_sample_name(name):
    return '_'.join(name.split('_')[:-1])


# RLGS8 removed
sub_sample_codes_list = ['RLGS1', 'RLGS2', 'RLGS3', 'RLGS4', 'RLGS5', 'RLGS6', 'RLGS7', 'RLGS9', 'RLGS10', 'RLGS11', 'RLGS12']

# ecDNA dictioanry
from region_dict import ecdna_sample_dict




coral_samples_csv = "RLGS1-primary,RLGS1-recurrent,RLGS2-primary,RLGS2-recurrent,RLGS3-primary,RLGS3-recurrent,RLGS4-primary,RLGS4-recurrent,RLGS5-primary,RLGS5-recurrent,RLGS6-primary,RLGS6-recurrent,RLGS7-primary,RLGS7-recurrent,RLGS8-primary,RLGS8-recurrent,RLGS9-recurrent,RLGS10-primary,RLGS10-recurrent,RLGS11-primary,RLGS11-recurrent,RLGS12-primary,RLGS12-recurrent"
coral_sample_list = coral_samples_csv.split(",")

###################################
# Complete workflow
###################################

rule all:
    input:
        # code to execute epi2me pipelines
        expand("{bin_dir}/epi2me_nextflow/human_variation/{run_type}-wf_human_variation_blood_control-{sample_code}-{tumor}", bin_dir=BIN_DIR, run_type='nanopore_human_variation', sample_code=sample_codes_list, tumor=['blood']),


        ## ecDNA analysis
        # Manual
        # RES_DIR + "/analysis_nanopore/ecdna/region_dict.tsv",
        # expand(RES_DIR + "/analysis_nanopore/ecdna/manual_assembly/{patient}-{tumor}/assembly_summary.tsv", patient=['RLGS2'], tumor=['primary']),

        # CoRAL
        # expand(RES_DIR + "/analysis_nanopore/ecdna/coral/{patient}-{tumor}/{patient}-{tumor}_CNV_SEEDS.bed", patient=sample_codes_list, tumor=tumor_types),
        # expand(RES_DIR + "/analysis_nanopore/ecdna/coral/{sample}/{sample}_CNV_SEEDS.bed", sample=coral_sample_list),
        # expand(RES_DIR + "/analysis_nanopore/ecdna/coral/{sample}/{sample}_graph.txt", sample=coral_sample_list),
        # RES_DIR + "/analysis_nanopore/ecdna/coral/RLGS2-primary/RLGS2-primary_graph.txt",

        # expand("{res_dir}/analysis_nanopore/ecdna/cnvkit/{sample}/cnvkit.cns", res_dir = RES_DIR, sample = ['RLGS2-primary', 'RLGS2-recurrent']),

        # decoil
        expand("{res_dir}/analysis_nanopore/ecdna/decoil/{sample}/reconstruct.ecDNA.filtered.fasta", res_dir = RES_DIR, sample = coral_sample_list),
        RES_DIR + "/analysis_nanopore/ecdna/_figures/decoil_summary.pdf",

        # WGS ecDNA
        RES_DIR + "/circ_dna/_figures/amplicon_scatter_plot.pdf",
        expand("{bin_dir}/circ_dna/{sample}", bin_dir = BIN_DIR, sample = sample_list),




        ## NANOPORE
        ## Nanopore Methylation
        # methylation overview hex plot heatmap and histogram
        RES_DIR + "/analysis_nanopore/_figures_methyl_landscape/correlated_methylation_hexbin.pdf",
        RES_DIR + "/analysis_nanopore/_figures_methyl_landscape/mean_promoter_methylation_by_gene_sample.pdf",
        RES_DIR + "/analysis_nanopore/_figures_methyl_landscape/cpg_promoter_methylation_by_sample.pdf",
        RES_DIR + "/analysis_nanopore/_figures_methyl_landscape/visualize_coverage_correlation_associations.pdf",
        RES_DIR + "/analysis_nanopore/_figures_methyl_landscape/pcg_promoter_umap.pdf",

        # testing MGMT promoter methylation
        RES_DIR + "/analysis_nanopore/_figures_diff_meth/visualize_merge_test_methylation_data_for_cpg_methylation.pdf",
        RES_DIR + "/analysis_nanopore/_figures_diff_meth/visualize_merge_test_methylation_data.pdf",

        # differential methylation analysis using 300bp promoter method
        RES_DIR + "/analysis_nanopore/_figures_diff_meth/volcano_plot_methylation_pvalues_defined_prom.pdf",
        RES_DIR + "/analysis_nanopore/_figures_diff_meth/methylation_promoter_boxplots.pdf",
        RES_DIR + "/analysis_nanopore/diff_promoter_methylation/mean_methylation_by_gene_and_sample.tsv",


        ## NANOPORE SVs
        ## Nanopore SV analysis
        RES_DIR + "/analysis_nanopore/svs_figures/jasmine_overlaps_proportions.pdf",
        RES_DIR + "/analysis_nanopore/svs_figures/visualize_nanopore_sv_examples.pdf",
        RES_DIR + "/analysis_nanopore/svs_figures/visualize_exon_nanopore_sv_examples.pdf",

        # tumors vs. control SVs
        RES_DIR + "/analysis_nanopore/processing_non_somatic_svs/spec_sv_counts.tsv",
        RES_DIR + "/analysis_nanopore/svs_figures/control_exon_nanopore_sv_examples.pdf",

        # control only SVs
        RES_DIR + "/analysis_nanopore/svs_figures/control_nanopore_sv_examples.pdf",
        
        # tumor only SVs
        RES_DIR + "/analysis_nanopore/svs_figures/tumor_only_exon_nanopore_sv_examples.pdf",
        RES_DIR + "/analysis_nanopore/svs_figures/visualize_exon_nanopore_sv_examples_severus.pdf",

        # severus protein-coding SVs
        RES_DIR + "/analysis_nanopore/svs_figures/visualize_exon_nanopore_sv_examples_severus.pdf",
        RES_DIR + "/analysis_nanopore/svs_figures/visualize_nanopore_sv_examples_severus.pdf",


        # Nanopore SVs vs. Illumina SVs
        RES_DIR + "/analysis_nanopore/svs_figures/common_gene_svs_illumina_nanopore.pdf",
        RES_DIR + "/analysis_nanopore/svs_figures/sv_numbers_and_lengths_nanopore_illumina.pdf",
        RES_DIR + "/analysis_nanopore/svs_figures/circos_bnd_comparison.pdf",
        RES_DIR + "/analysis_nanopore/sv_overlaps/nanopore_vs_illumina_processed/illumina_nanopore_overlap_fractions_stats.tsv",


        # RES_DIR + "/analysis_nanopore/svs_figures/sv_lengths_nanopore_illumina.pdf",
        # RES_DIR + "/analysis_nanopore/svs_figures/jasmine_results_illumina_overlap_proportions.pdf",


        # # testing EGFRvIII using raw, unfiltered VCFs
        # RES_DIR + "/analysis_nanopore/tmp/figures/tumor_only_exon_nanopore_sv_examples.pdf",
        # RES_DIR + "/analysis_nanopore/tmp/figures/tumor_only_nanopore_sv_examples.pdf",

        # # Nanopore ecDNA and episomes
        # expand("{results_dir}/analysis_nanopore/episomes/fastq_files/{region}.fastq", results_dir = RES_DIR , region=region_list),
        # RES_DIR + "/analysis_nanopore/episomes/_figures/histogram_read_lengths_fastqs.pdf",
        # RES_DIR + "/analysis_nanopore/episomes/prioritized_cnas_for_episomes.tsv",
        # # expand("{results_dir}/analysis_nanopore/episomes/miniasm_assemblies/{region}-miniasm.gfa", results_dir = RES_DIR , region=sub_region_list),



        ## SAREK
        ## SAREK SNVs
        # post-processing summary
        RES_DIR + "/analysis_sarek/_figures/001-oncoprint_cgc_genes.pdf",
        RES_DIR + "/analysis_sarek/_figures/002-visualize_maf.pdf",
        RES_DIR + "/analysis_sarek/_figures/002-visualize_overlapping_cgc_mutations.pdf",
        RES_DIR + "/analysis_sarek/_figures/coding_impacts_of_snvs_indels.pdf",

        # summarize variants and cgc genes
        RES_DIR + "/analysis_sarek/_figures/oncoprint_cgc_genes.pdf",


        # SAREK CNAs
        RES_DIR + "/analysis_sarek/_figures/008-circos_cnas.pdf",

        RES_DIR + "/analysis_sarek/_figures/visualize_pcg_cnas_oncoprint.pdf",
        RES_DIR + "/analysis_sarek/_figures/visualize_coverage_at_cna_genes_ascat.pdf",
        RES_DIR + "/analysis_sarek/_figures/visualize_coverage_at_cna_genes_ascat_exons.pdf",

        # cna expression correlations
        RES_DIR + "/analysis_sarek/_figures/visualize_differential_expression_by_cna.pdf",




        # SAREK evolutionary analysis
        RES_DIR + "/analysis_sarek/_figures/010-classify_mutations_primary_recurrent_visualize.pdf",
        RES_DIR + "/analysis_sarek/_figures/cna_fractions_primary_recurrent.pdf",
        RES_DIR + "/analysis_overview/_figure_data/compare_snv_vs_sv_and_cna_stats.tsv",

        # pyclone
        RES_DIR + "/analysis_sarek/pyclone/output/pyclone_vi-combined_results.tsv",

        # sigprofiler
        RES_DIR + "/analysis_sarek/_figures/002-sigprofiler_by_sample.pdf",
        RES_DIR + "/analysis_sarek/_figures/visualize_paired_nucleotide_mutation_stats.pdf",
        RES_DIR + "/analysis_sarek/sigprofiler/similarity_stats.tsv",

        # # snv mutation rates
        # RES_DIR + "/analysis_sarek/_figures/vaf_mutations_histogram.pdf",
        # RES_DIR + "/analysis_sarek/_figures/visualize_gmm_clusters.pdf",
        # RES_DIR + "/analysis_sarek/_figures/visualize_gmm_stats.pdf",


        # CANOPY
        # RES_DIR + "/analysis_sarek/canopy/canopy_figures/canopy-RLGS3-subset_mutations-canopy_figures.pdf",
        # expand("{results_dir}/analysis_sarek/canopy/canopy_figures/canopy-{patient}-subset_mutations-canopy_figures.pdf", results_dir = RES_DIR , patient=sub_sample_codes_list),
        # expand("{results_dir}/analysis_sarek/canopy/canopy_r_results/canopy-{patient}.Rdata", results_dir = RES_DIR , patient=sub_sample_codes_list),
        # RES_DIR + "/analysis_sarek/canopy/_figures/canopy-RLGS1_canopy_figures.pdf",
        # RES_DIR + "/analysis_sarek/canopy/testing_results/canopy-RLGS1.Rdata",



        # SAREK combining multi-omic combinations
        # cna
        RES_DIR + "/analysis_sarek/consensus_cnas/cna_gene_contingency_file.tsv",
        RES_DIR + "/analysis_sarek/consensus_cnas/cna_gene_continuous_stats.tsv",

        # svs
        RES_DIR + "/analysis_sarek/sv_figures/visualize_illumina_sv_examples.pdf",
        RES_DIR + "/analysis_sarek/sv_figures/visualize_exon_nanopore_sv_examples.pdf",

        # snv
        RES_DIR + "/analysis_sarek/consensus_vcfs/vcf_contingency_table_stats.tsv",
        RES_DIR + "/analysis_sarek/vcf_figure_data/snv_nb_differentially_mutated.tsv",



        # RNA-SEQ
        RES_DIR + "/analysis_rna/_figures/001-visualize_umap_tpm.pdf",
        RES_DIR + "/analysis_rna/_figures/001-rna_correlation_visualize_faceted.pdf",

        RES_DIR + "/analysis_rna/_figures/002-visualize_volcano_plot_rnaseq.pdf",
        RES_DIR + "/analysis_rna/_figures/002-visualize_dgea_barplots.pdf",
        RES_DIR + "/analysis_rna/_figures/004-pvalue_distributions.pdf",

        # # correlations with methylation
        # RES_DIR + "/analysis_rna/_figures/004-scatterplot_rna_methylation_pvalues.pdf",
        # RES_DIR + "/analysis_nanopore/_figures/006-methylation_transcript_correlations.pdf",

        # RES_DIR + "/analysis_rna/_figures/correlate_methylation_expression_per_gene_stats.pdf",



        # MULTI-OMICS
        # pathway enrichment
        RES_DIR + "/analysis_rna/pathway_enrichment/rna_methylation_cna_snv/enriched_pathways.csv",

        RES_DIR + "/analysis_nanopore/svs/nanopore_wgs_cnas_svs_combined.tsv",

        # pathway enrichment dotplot decomposition
        RES_DIR + "/analysis_rna/_figures/visualize_pathway_contributions_snv_cna_rna_methylation.pdf",



        # CLINICAL OVERVIEW 
        RES_DIR + "/analysis_rna/_figures/010-clinical_sequencing_summary_figure.pdf",
        RES_DIR + "/analysis_overview/_figures/001-clinical_sequencing_summary_figure.pdf",
        RES_DIR + "/analysis_overview/_figures/patient_treatment_summary_figure.pdf",

        # COMBINED ONCOPRINT OVERVIEW
        RES_DIR + "/analysis_overview/_figures/combined_oncoprint.pdf",

        # SUBTYPE CLASSIFICATIONS
        expand("{res_dir}/analysis_nanopore/sturgeon/results/{sample}-merged_probes_methyl_calls_general.pdf", res_dir = RES_DIR, sample = sample_list),
        RES_DIR + "/analysis_rna/_figures/010-clinical_sequencing_summary_figure.pdf",

        # SAREK summarize mutations GLASS
        RES_DIR + "/analysis_sarek/_figures/002-summarize_nucleotide_mutations.pdf",
        RES_DIR + "/analysis_sarek/_figures/003-compare_mutation_counts_glass_visualize.pdf",
        tmp = RES_DIR + "/analysis_sarek/_figures/003-compare_mutation_freqs_paired_glass_visualize.pdf"





# create main project directories
rule main_directories:
    output:
        res = RES_DIR + "/null.txt",
        figure_data = FIGURE_DATA_DIR + "/null.txt",
        figures = FIGURE_DIR + "/null.txt"
        # genome_fasta,
        
    resources:
        threads = 1,
        queue = "all.q",
        jobtime = '0:112:0:0',
        individual_core_memory = '10G'
        
    run:
        shell("mkdir -p {RES_DIR} {FIGURE_DATA_DIR} {FIGURE_DIR}")
        shell("touch {output.res} {output.figure_data} {output.figures}")
        # shell("zcat {original_genome_fasta} > {genome_fasta}")





include: "snakemake/1000-testing.smk"

#####################
# nanopore
#####################
include: "snakemake/100-nanopore_nextflow.smk"

# methylation
include: "snakemake/101-methylation_landscape.smk"
include: "snakemake/102-diff_promoter_methylation.smk"
include: "snakemake/103-mgmt_diff_methylation_validation.smk"
# include: "snakemake/103-methylation_rnaseq_correlations.smk"

# methylation overview and subtyping
include: "snakemake/110-nanopore_classifications.smk"


# SVs and CNAs
include: "snakemake/121-nanopore_structural_variants.smk"
include: "snakemake/122-nanopore_severus_svs.smk"
include: "snakemake/124-control_structural_variants.smk"
include: "snakemake/125-tumor_only_svs_sniffles2.smk"

# SVs and SV comparisons
include: "snakemake/131-svs_comparison.smk"


#####################
# ecDNA
#####################
include: "snakemake/141-manual_ecdna_reconstruction.smk"
include: "snakemake/142-nanopore_ecdna.smk"
include: "snakemake/143-wgs_ecdna.smk"


#####################
# WGS Sarek
#####################
include: "snakemake/200-sarek_wgs_nextflow.smk"
include: "snakemake/201-wgs_sarek_vcf_post_processing.smk"
include: "snakemake/202-wgs_summary.smk"

# evolution
include: "snakemake/204-wgs_evolution.smk"
include: "snakemake/205-wgs_phylogenic_evolution.smk"

# CNAs
include: "snakemake/211-wgs_cnas_processing.smk"
include: "snakemake/212-wgs_cnas_visualization.smk"

# SVs
include: "snakemake/213-wgs_svs.smk"

# pathways enrichments
include: "snakemake/220-snv_cna_gene_pvalue_generation.smk"
include: "snakemake/221-pathway_enrichments.smk"


#####################
# RNA Sarek
#####################
include: "snakemake/301-rnaseq_umaps_correlations.smk"
include: "snakemake/302-dgea.smk"

# gene promoter methylation vs. expression correlations
include: "snakemake/303-methylation_rnaseq_correlations.smk"


#####################
# sequencing and clinical overview 
#####################
include: "snakemake/401-summary_figures.smk"

